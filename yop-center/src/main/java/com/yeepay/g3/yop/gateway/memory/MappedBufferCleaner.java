/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 内存映射缓冲区清理工具<br/>
 * description: 提供直接内存和内存映射文件的清理机制，防止内存泄漏<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class MappedBufferCleaner {

    private static final Logger log = Logger.getLogger(MappedBufferCleaner.class.getName());
    
    // 缓存反射方法以提高性能
    private static volatile Method cleanerMethod;
    private static volatile Method cleanMethod;
    private static volatile boolean cleanerAvailable = true;
    
    static {
        initializeCleaner();
    }
    
    /**
     * 初始化清理器反射方法
     */
    private static void initializeCleaner() {
        try {
            // 尝试获取DirectByteBuffer的cleaner方法
            Class<?> directBufferClass = Class.forName("java.nio.DirectByteBuffer");
            cleanerMethod = directBufferClass.getMethod("cleaner");
            cleanerMethod.setAccessible(true);
            
            // 获取Cleaner的clean方法
            Object testBuffer = ByteBuffer.allocateDirect(1);
            Object cleaner = cleanerMethod.invoke(testBuffer);
            if (cleaner != null) {
                cleanMethod = cleaner.getClass().getMethod("clean");
                cleanMethod.setAccessible(true);
            }
            
            log.info("DirectByteBuffer cleaner initialized successfully");
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Failed to initialize DirectByteBuffer cleaner, " +
                   "direct memory cleanup will rely on GC", e);
            cleanerAvailable = false;
        }
    }
    
    /**
     * 清理直接内存缓冲区
     * 
     * @param buffer 要清理的ByteBuffer
     * @return 是否成功清理
     */
    public static boolean cleanDirectBuffer(ByteBuffer buffer) {
        if (buffer == null || !buffer.isDirect()) {
            return false;
        }
        
        if (!cleanerAvailable) {
            return false;
        }
        
        try {
            Object cleaner = cleanerMethod.invoke(buffer);
            if (cleaner != null) {
                cleanMethod.invoke(cleaner);
                return true;
            }
        } catch (Exception e) {
            log.log(Level.FINE, "Failed to clean direct buffer", e);
        }
        
        return false;
    }
    
    /**
     * 批量清理直接内存缓冲区
     * 
     * @param buffers 要清理的ByteBuffer数组
     * @return 成功清理的缓冲区数量
     */
    public static int cleanDirectBuffers(ByteBuffer... buffers) {
        if (buffers == null || buffers.length == 0) {
            return 0;
        }
        
        int cleanedCount = 0;
        for (ByteBuffer buffer : buffers) {
            if (cleanDirectBuffer(buffer)) {
                cleanedCount++;
            }
        }
        
        return cleanedCount;
    }
    
    /**
     * 安全地清理直接内存缓冲区（忽略所有异常）
     * 
     * @param buffer 要清理的ByteBuffer
     */
    public static void cleanDirectBufferSafely(ByteBuffer buffer) {
        try {
            cleanDirectBuffer(buffer);
        } catch (Exception e) {
            // 静默处理，避免影响主流程
        }
    }
    
    /**
     * 检查清理器是否可用
     * 
     * @return 清理器是否可用
     */
    public static boolean isCleanerAvailable() {
        return cleanerAvailable;
    }
    
    /**
     * 获取直接内存使用情况
     * 
     * @return 直接内存使用信息
     */
    public static DirectMemoryInfo getDirectMemoryInfo() {
        DirectMemoryInfo info = new DirectMemoryInfo();
        
        try {
            // 尝试获取直接内存使用情况
            Class<?> vmClass = Class.forName("sun.misc.VM");
            Method maxDirectMemoryMethod = vmClass.getMethod("maxDirectMemory");
            long maxDirectMemory = (Long) maxDirectMemoryMethod.invoke(null);
            info.maxDirectMemory = maxDirectMemory;
            
            // 获取已使用的直接内存（这个方法在不同JVM版本中可能不同）
            try {
                Class<?> bitsClass = Class.forName("java.nio.Bits");
                Method reservedMemoryMethod = bitsClass.getDeclaredMethod("reservedMemory");
                reservedMemoryMethod.setAccessible(true);
                long usedDirectMemory = (Long) reservedMemoryMethod.invoke(null);
                info.usedDirectMemory = usedDirectMemory;
            } catch (Exception e) {
                // 如果无法获取已使用内存，设置为-1表示未知
                info.usedDirectMemory = -1;
            }
            
        } catch (Exception e) {
            log.log(Level.FINE, "Failed to get direct memory info", e);
            info.maxDirectMemory = -1;
            info.usedDirectMemory = -1;
        }
        
        return info;
    }
    
    /**
     * 强制执行垃圾回收以清理未引用的直接内存
     * 注意：这个方法应该谨慎使用，因为它会触发Full GC
     */
    public static void forceDirectMemoryCleanup() {
        log.warning("Forcing direct memory cleanup via GC - this may cause performance impact");
        
        // 连续调用几次GC以确保清理
        for (int i = 0; i < 3; i++) {
            System.gc();
            try {
                Thread.sleep(100); // 给GC一些时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 直接内存信息类
     */
    public static class DirectMemoryInfo {
        private long maxDirectMemory = -1;
        private long usedDirectMemory = -1;
        
        public long getMaxDirectMemory() {
            return maxDirectMemory;
        }
        
        public long getUsedDirectMemory() {
            return usedDirectMemory;
        }
        
        public long getAvailableDirectMemory() {
            if (maxDirectMemory > 0 && usedDirectMemory >= 0) {
                return maxDirectMemory - usedDirectMemory;
            }
            return -1;
        }
        
        public double getUsagePercentage() {
            if (maxDirectMemory > 0 && usedDirectMemory >= 0) {
                return (double) usedDirectMemory / maxDirectMemory * 100;
            }
            return -1;
        }
        
        @Override
        public String toString() {
            if (maxDirectMemory > 0) {
                return String.format("DirectMemoryInfo{max=%dMB, used=%dMB, available=%dMB, usage=%.2f%%}",
                    maxDirectMemory / 1024 / 1024,
                    usedDirectMemory >= 0 ? usedDirectMemory / 1024 / 1024 : -1,
                    getAvailableDirectMemory() >= 0 ? getAvailableDirectMemory() / 1024 / 1024 : -1,
                    getUsagePercentage());
            } else {
                return "DirectMemoryInfo{unavailable}";
            }
        }
    }
    
    /**
     * 内存清理统计信息
     */
    public static class CleanupStats {
        private int totalCleanupAttempts = 0;
        private int successfulCleanups = 0;
        private int failedCleanups = 0;
        
        public void recordCleanupAttempt(boolean success) {
            totalCleanupAttempts++;
            if (success) {
                successfulCleanups++;
            } else {
                failedCleanups++;
            }
        }
        
        public int getTotalCleanupAttempts() {
            return totalCleanupAttempts;
        }
        
        public int getSuccessfulCleanups() {
            return successfulCleanups;
        }
        
        public int getFailedCleanups() {
            return failedCleanups;
        }
        
        public double getSuccessRate() {
            return totalCleanupAttempts > 0 ? 
                (double) successfulCleanups / totalCleanupAttempts * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format("CleanupStats{total=%d, success=%d, failed=%d, successRate=%.2f%%}",
                totalCleanupAttempts, successfulCleanups, failedCleanups, getSuccessRate());
        }
    }
}
