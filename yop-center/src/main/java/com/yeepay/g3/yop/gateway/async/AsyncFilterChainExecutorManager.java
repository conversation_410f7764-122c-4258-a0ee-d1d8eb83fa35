/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.async;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * title: 异步过滤器链线程池管理器<br/>
 * description: 管理异步过滤器链使用的线程池，提供优雅关闭机制<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
@Slf4j
public class AsyncFilterChainExecutorManager {

    private static volatile AsyncFilterChainExecutorManager INSTANCE;
    private static final Object LOCK = new Object();

    private final ExecutorService businessExecutor;
    private final ScheduledExecutorService timeoutExecutor;
    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    private AsyncFilterChainExecutorManager() {
        this.businessExecutor = createBusinessExecutor();
        this.timeoutExecutor = createTimeoutExecutor();
        
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown, "AsyncFilterChain-Shutdown"));
    }

    /**
     * 获取单例实例
     */
    public static AsyncFilterChainExecutorManager getInstance() {
        if (INSTANCE == null) {
            synchronized (LOCK) {
                if (INSTANCE == null) {
                    INSTANCE = new AsyncFilterChainExecutorManager();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 获取业务线程池
     */
    public ExecutorService getBusinessExecutor() {
        if (shutdown.get()) {
            throw new IllegalStateException("ExecutorManager has been shutdown");
        }
        return businessExecutor;
    }

    /**
     * 获取超时控制线程池
     */
    public ScheduledExecutorService getTimeoutExecutor() {
        if (shutdown.get()) {
            throw new IllegalStateException("ExecutorManager has been shutdown");
        }
        return timeoutExecutor;
    }

    /**
     * 创建业务线程池
     */
    private ExecutorService createBusinessExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                10, 100, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> {
                    Thread t = new Thread(r, "async-filter-business-" + System.currentTimeMillis());
                    t.setDaemon(true);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 允许核心线程超时
        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 创建超时控制线程池
     */
    private ScheduledExecutorService createTimeoutExecutor() {
        return Executors.newScheduledThreadPool(5, r -> {
            Thread t = new Thread(r, "async-filter-timeout-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * 优雅关闭所有线程池
     */
    public void shutdown() {
        if (shutdown.compareAndSet(false, true)) {
            log.info("Shutting down AsyncFilterChain executors...");
            
            try {
                // 关闭业务线程池
                businessExecutor.shutdown();
                if (!businessExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warning("Business executor did not terminate gracefully, forcing shutdown");
                    businessExecutor.shutdownNow();
                }
                
                // 关闭超时线程池
                timeoutExecutor.shutdown();
                if (!timeoutExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warning("Timeout executor did not terminate gracefully, forcing shutdown");
                    timeoutExecutor.shutdownNow();
                }
                
                log.info("AsyncFilterChain executors shutdown completed");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.severe("Interrupted while shutting down executors");
                
                // 强制关闭
                businessExecutor.shutdownNow();
                timeoutExecutor.shutdownNow();
            }
        }
    }

    /**
     * 检查是否已关闭
     */
    public boolean isShutdown() {
        return shutdown.get();
    }

    /**
     * 获取线程池状态信息
     */
    public ExecutorStatus getExecutorStatus() {
        ExecutorStatus status = new ExecutorStatus();
        status.setShutdown(shutdown.get());
        
        if (businessExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) businessExecutor;
            status.setBusinessPoolSize(tpe.getPoolSize());
            status.setBusinessActiveCount(tpe.getActiveCount());
            status.setBusinessQueueSize(tpe.getQueue().size());
            status.setBusinessCompletedTaskCount(tpe.getCompletedTaskCount());
        }
        
        if (timeoutExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) timeoutExecutor;
            status.setTimeoutPoolSize(tpe.getPoolSize());
            status.setTimeoutActiveCount(tpe.getActiveCount());
            status.setTimeoutQueueSize(tpe.getQueue().size());
        }
        
        return status;
    }

    /**
     * 线程池状态信息
     */
    public static class ExecutorStatus {
        private boolean shutdown;
        private int businessPoolSize;
        private int businessActiveCount;
        private int businessQueueSize;
        private long businessCompletedTaskCount;
        private int timeoutPoolSize;
        private int timeoutActiveCount;
        private int timeoutQueueSize;

        // Getters and Setters
        public boolean isShutdown() { return shutdown; }
        public void setShutdown(boolean shutdown) { this.shutdown = shutdown; }
        
        public int getBusinessPoolSize() { return businessPoolSize; }
        public void setBusinessPoolSize(int businessPoolSize) { this.businessPoolSize = businessPoolSize; }
        
        public int getBusinessActiveCount() { return businessActiveCount; }
        public void setBusinessActiveCount(int businessActiveCount) { this.businessActiveCount = businessActiveCount; }
        
        public int getBusinessQueueSize() { return businessQueueSize; }
        public void setBusinessQueueSize(int businessQueueSize) { this.businessQueueSize = businessQueueSize; }
        
        public long getBusinessCompletedTaskCount() { return businessCompletedTaskCount; }
        public void setBusinessCompletedTaskCount(long businessCompletedTaskCount) { this.businessCompletedTaskCount = businessCompletedTaskCount; }
        
        public int getTimeoutPoolSize() { return timeoutPoolSize; }
        public void setTimeoutPoolSize(int timeoutPoolSize) { this.timeoutPoolSize = timeoutPoolSize; }
        
        public int getTimeoutActiveCount() { return timeoutActiveCount; }
        public void setTimeoutActiveCount(int timeoutActiveCount) { this.timeoutActiveCount = timeoutActiveCount; }
        
        public int getTimeoutQueueSize() { return timeoutQueueSize; }
        public void setTimeoutQueueSize(int timeoutQueueSize) { this.timeoutQueueSize = timeoutQueueSize; }
    }
}
