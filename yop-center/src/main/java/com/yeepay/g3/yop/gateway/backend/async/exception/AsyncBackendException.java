/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async.exception;

/**
 * title: 异步后端异常基类<br/>
 * description: 异步后端调用相关的异常基类<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class AsyncBackendException extends RuntimeException {

    private final String backendName;
    private final ErrorCode errorCode;

    public AsyncBackendException(String backendName, ErrorCode errorCode, String message) {
        super(message);
        this.backendName = backendName;
        this.errorCode = errorCode;
    }

    public AsyncBackendException(String backendName, ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.backendName = backendName;
        this.errorCode = errorCode;
    }

    public String getBackendName() {
        return backendName;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 错误码枚举
     */
    public enum ErrorCode {
        TIMEOUT("TIMEOUT", "调用超时"),
        CIRCUIT_BREAKER_OPEN("CIRCUIT_BREAKER_OPEN", "熔断器打开"),
        MAX_RETRY_EXCEEDED("MAX_RETRY_EXCEEDED", "超过最大重试次数"),
        CONNECTION_FAILED("CONNECTION_FAILED", "连接失败"),
        INVALID_RESPONSE("INVALID_RESPONSE", "无效响应"),
        CONFIG_ERROR("CONFIG_ERROR", "配置错误"),
        UNKNOWN_ERROR("UNKNOWN_ERROR", "未知错误");

        private final String code;
        private final String description;

        ErrorCode(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
