/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async;

import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.gateway.backend.async.exception.AsyncBackendException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * title: 异步后端测试<br/>
 * description: 测试异步后端的基本功能和资源管理<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class AsyncBackendTest {

    @Mock
    private AsyncServerWebExchange mockExchange;

    private TestAsyncBackend testBackend;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testBackend = new TestAsyncBackend();
    }

    @AfterEach
    void tearDown() throws Exception {
        if (testBackend != null && !testBackend.isClosed()) {
            testBackend.close();
        }
    }

    @Test
    void testBasicInvocation() throws Exception {
        // 测试基本调用
        CompletableFuture<Object> result = testBackend.invokeAsync(mockExchange);
        assertNotNull(result);
        
        Object value = result.get(5, TimeUnit.SECONDS);
        assertEquals("test-result", value);
    }

    @Test
    void testClosedBackendThrowsException() throws Exception {
        // 关闭后端
        testBackend.close();
        
        // 尝试调用应该抛出异常
        CompletableFuture<Object> result = testBackend.invokeAsync(mockExchange);
        
        assertThrows(AsyncBackendException.class, () -> {
            result.get(1, TimeUnit.SECONDS);
        });
    }

    @Test
    void testResourceCleanup() throws Exception {
        // 验证资源清理
        assertFalse(testBackend.isClosed());
        
        testBackend.close();
        
        assertTrue(testBackend.isClosed());
    }

    @Test
    void testHealthCheck() throws Exception {
        // 测试健康检查
        CompletableFuture<Boolean> healthResult = testBackend.healthCheck();
        assertTrue(healthResult.get(1, TimeUnit.SECONDS));
        
        // 关闭后健康检查应该返回false
        testBackend.close();
        CompletableFuture<Boolean> closedHealthResult = testBackend.healthCheck();
        assertFalse(closedHealthResult.get(1, TimeUnit.SECONDS));
    }

    /**
     * 测试用的异步后端实现
     */
    private static class TestAsyncBackend extends AbstractAsyncApiBackend {

        @Override
        protected CompletableFuture<Object> doInvokeAsync(AsyncServerWebExchange exchange) {
            return CompletableFuture.completedFuture("test-result");
        }

        @Override
        public String getName() {
            return "TestAsyncBackend";
        }

        @Override
        public BackendType getBackendType() {
            return BackendType.METHOD;
        }

        @Override
        public boolean supportsHealthCheck() {
            return true;
        }

        @Override
        public boolean supportsRetry() {
            return true;
        }

        @Override
        public int getRetryCount() {
            return 2;
        }

        @Override
        public boolean supportsCircuitBreaker() {
            return true;
        }

        @Override
        public long getTimeoutMillis() {
            return 5000L;
        }
    }
}
