/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async;

import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.frame.utils.YopConstants;
import com.yeepay.g3.yop.router.utils.ToStringHolder;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * title: 异步Dubbo后端实现<br/>
 * description: 基于Dubbo异步调用API实现的高性能异步后端<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class AsyncDubboBackend extends AbstractAsyncApiBackend {

    private static final Logger log = Logger.getLogger(AsyncDubboBackend.class.getName());

    // Dubbo服务引用配置类名（避免直接依赖）
    private Object referenceConfig;
    
    // 缓存的服务实例
    private volatile Object serviceInstance;
    
    // 方法缓存
    private final Map<String, Method> methodCache = new ConcurrentHashMap<>();
    
    // 服务接口类
    private Class<?> serviceInterface;
    
    // 目标方法
    private Method targetMethod;
    
    // Dubbo连接池配置
    private DubboConnectionPoolConfig poolConfig;

    public AsyncDubboBackend() {
        super();
    }

    @Override
    protected CompletableFuture<Object> doInvokeAsync(AsyncServerWebExchange exchange) {
        try {
            // 获取绑定的参数
            Map<String, Object> params = getBoundParams(exchange);
            
            // 获取服务实例
            Object service = getServiceInstance();
            
            // 准备调用参数
            Object[] args = prepareInvokeArgs(params);
            
            log.fine("Dubbo async call: " + targetMethod.getName() + " with args: " + ToStringHolder.objectToString(args));
            
            // 执行异步Dubbo调用
            return executeInIOPool(() -> {
                try {
                    // 检查方法是否返回CompletableFuture
                    Object result = targetMethod.invoke(service, args);
                    
                    if (result instanceof CompletableFuture) {
                        // 如果是异步方法，直接返回CompletableFuture
                        return ((CompletableFuture<Object>) result).get();
                    } else {
                        // 如果是同步方法，直接返回结果
                        return result;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Dubbo async call failed", e);
                }
            });
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "Failed to invoke Dubbo service", e);
            CompletableFuture<Object> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * 从AsyncServerWebExchange获取绑定参数
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getBoundParams(AsyncServerWebExchange exchange) {
        // 从exchange的attributes中获取绑定参数
        return (Map<String, Object>) exchange.getAttribute("boundParams");
    }

    /**
     * 准备调用参数
     */
    private Object[] prepareInvokeArgs(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return new Object[0];
        }
        
        // 检查是否有预处理的调用参数
        Object invokeArgs = params.get(YopConstants.BACKEND_INVOKE_ARGS_KEY);
        if (invokeArgs != null) {
            return (Object[]) invokeArgs;
        }
        
        // 将参数转换为数组
        Object[] args = new Object[params.size()];
        int i = 0;
        for (Object value : params.values()) {
            args[i++] = value;
        }
        
        return args;
    }

    /**
     * 获取服务实例（懒加载）
     */
    private Object getServiceInstance() {
        if (serviceInstance == null) {
            synchronized (this) {
                if (serviceInstance == null) {
                    if (referenceConfig == null) {
                        throw new IllegalStateException("ReferenceConfig not initialized");
                    }
                    
                    // 配置连接池参数
                    configureConnectionPool();
                    
                    // 通过反射获取服务实例
                    serviceInstance = getServiceFromConfig();
                    
                    log.info("Dubbo service instance initialized: " + serviceInterface.getName());
                }
            }
        }
        return serviceInstance;
    }

    /**
     * 从配置中获取服务实例
     */
    private Object getServiceFromConfig() {
        try {
            // 通过反射调用ReferenceConfig.get()方法
            Method getMethod = referenceConfig.getClass().getMethod("get");
            return getMethod.invoke(referenceConfig);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get service instance from ReferenceConfig", e);
        }
    }

    /**
     * 配置Dubbo连接池
     */
    private void configureConnectionPool() {
        if (poolConfig != null && referenceConfig != null) {
            try {
                Class<?> configClass = referenceConfig.getClass();
                
                // 设置连接数
                if (poolConfig.getConnections() > 0) {
                    setConfigProperty(configClass, "setConnections", poolConfig.getConnections());
                }
                
                // 设置超时时间
                if (poolConfig.getTimeout() > 0) {
                    setConfigProperty(configClass, "setTimeout", poolConfig.getTimeout());
                }
                
                // 设置重试次数
                if (poolConfig.getRetries() >= 0) {
                    setConfigProperty(configClass, "setRetries", poolConfig.getRetries());
                }
                
                // 设置负载均衡策略
                if (poolConfig.getLoadBalance() != null) {
                    setConfigProperty(configClass, "setLoadbalance", poolConfig.getLoadBalance());
                }
                
                // 设置集群容错策略
                if (poolConfig.getCluster() != null) {
                    setConfigProperty(configClass, "setCluster", poolConfig.getCluster());
                }
                
            } catch (Exception e) {
                log.log(Level.WARNING, "Failed to configure connection pool", e);
            }
        }
    }

    /**
     * 设置配置属性
     */
    private void setConfigProperty(Class<?> configClass, String methodName, Object value) {
        try {
            Method method = configClass.getMethod(methodName, value.getClass());
            method.invoke(referenceConfig, value);
        } catch (Exception e) {
            log.log(Level.WARNING, "Failed to set property: " + methodName, e);
        }
    }

    @Override
    protected boolean doHealthCheck() {
        try {
            Object service = getServiceInstance();
            // 执行一个轻量级的健康检查
            return service != null;
        } catch (Exception e) {
            log.log(Level.WARNING, "Dubbo health check failed", e);
            return false;
        }
    }

    @Override
    public BackendType getBackendType() {
        return BackendType.DUBBO;
    }

    @Override
    public boolean supportsHealthCheck() {
        return true;
    }

    @Override
    public boolean supportsRetry() {
        return true;
    }

    @Override
    public int getRetryCount() {
        return poolConfig != null ? poolConfig.getRetries() : 0;
    }

    @Override
    public boolean supportsCircuitBreaker() {
        return true;
    }

    @Override
    public long getTimeoutMillis() {
        return poolConfig != null ? poolConfig.getTimeout() : super.getTimeoutMillis();
    }

    @Override
    public LoadBalanceStrategy getLoadBalanceStrategy() {
        if (poolConfig != null && poolConfig.getLoadBalance() != null) {
            switch (poolConfig.getLoadBalance()) {
                case "roundrobin":
                    return LoadBalanceStrategy.ROUND_ROBIN;
                case "leastactive":
                    return LoadBalanceStrategy.LEAST_CONNECTIONS;
                case "consistenthash":
                    return LoadBalanceStrategy.CONSISTENT_HASH;
                case "random":
                    return LoadBalanceStrategy.RANDOM;
                default:
                    return LoadBalanceStrategy.ROUND_ROBIN;
            }
        }
        return super.getLoadBalanceStrategy();
    }

    // Getters and Setters
    public Object getReferenceConfig() {
        return referenceConfig;
    }

    public void setReferenceConfig(Object referenceConfig) {
        this.referenceConfig = referenceConfig;
    }

    public Class<?> getServiceInterface() {
        return serviceInterface;
    }

    public void setServiceInterface(Class<?> serviceInterface) {
        this.serviceInterface = serviceInterface;
    }

    public Method getTargetMethod() {
        return targetMethod;
    }

    public void setTargetMethod(Method targetMethod) {
        this.targetMethod = targetMethod;
    }

    public DubboConnectionPoolConfig getPoolConfig() {
        return poolConfig;
    }

    public void setPoolConfig(DubboConnectionPoolConfig poolConfig) {
        this.poolConfig = poolConfig;
    }

    /**
     * Dubbo连接池配置
     */
    public static class DubboConnectionPoolConfig {
        private int connections = 10;        // 连接数
        private int timeout = 5000;          // 超时时间(ms)
        private int retries = 0;             // 重试次数
        private String loadBalance = "roundrobin"; // 负载均衡策略
        private String cluster = "failover";       // 集群容错策略
        private boolean async = true;              // 是否异步调用
        private boolean sent = false;              // 是否等待发送完成

        // Getters and Setters
        public int getConnections() { return connections; }
        public void setConnections(int connections) { this.connections = connections; }
        
        public int getTimeout() { return timeout; }
        public void setTimeout(int timeout) { this.timeout = timeout; }
        
        public int getRetries() { return retries; }
        public void setRetries(int retries) { this.retries = retries; }
        
        public String getLoadBalance() { return loadBalance; }
        public void setLoadBalance(String loadBalance) { this.loadBalance = loadBalance; }
        
        public String getCluster() { return cluster; }
        public void setCluster(String cluster) { this.cluster = cluster; }
        
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
        
        public boolean isSent() { return sent; }
        public void setSent(boolean sent) { this.sent = sent; }
    }

    @Override
    public void close() throws Exception {
        super.close();

        // 清理Dubbo相关资源
        if (referenceConfig != null) {
            try {
                // 通过反射调用destroy方法
                Method destroyMethod = referenceConfig.getClass().getMethod("destroy");
                destroyMethod.invoke(referenceConfig);
                log.info("Dubbo reference destroyed for backend: " + getName());
            } catch (Exception e) {
                log.log(Level.WARNING, "Failed to destroy Dubbo reference", e);
            }
        }

        // 清理缓存
        methodCache.clear();
        serviceInstance = null;

        log.info("AsyncDubboBackend closed: " + getName());
    }
}