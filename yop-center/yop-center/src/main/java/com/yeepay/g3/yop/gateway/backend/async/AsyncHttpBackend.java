/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async;

import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.gateway.backend.async.exception.AsyncBackendException;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * title: 异步HTTP客户端后端实现<br/>
 * description: 基于异步HTTP客户端实现的高性能HTTP后端调用<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class AsyncHttpBackend extends AbstractAsyncApiBackend {

    private static final Logger log = Logger.getLogger(AsyncHttpBackend.class.getName());

    // 目标URL
    private String targetUrl;
    
    // HTTP方法
    private String httpMethod = "POST";
    
    // 连接池配置
    private HttpConnectionPoolConfig poolConfig;
    
    // 连接池状态统计
    private final ConnectionPoolStats poolStats = new ConnectionPoolStats();

    public AsyncHttpBackend() {
        super();
        this.poolConfig = new HttpConnectionPoolConfig();
    }

    @Override
    protected CompletableFuture<Object> doInvokeAsync(AsyncServerWebExchange exchange) {
        return executeInIOPool(() -> {
            HttpURLConnection connection = null;
            try {
                // 创建HTTP连接
                connection = createHttpConnection();
                
                // 设置请求头
                setupRequestHeaders(connection, exchange);
                
                // 发送请求体
                sendRequestBody(connection, exchange);
                
                // 读取响应
                return readResponse(connection);
                
            } catch (Exception e) {
                log.log(Level.SEVERE, "HTTP request failed to " + targetUrl, e);
                throw new RuntimeException("HTTP request failed", e);
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
                poolStats.recordConnectionUsed();
            }
        });
    }

    /**
     * 创建HTTP连接
     */
    private HttpURLConnection createHttpConnection() throws Exception {
        poolStats.recordConnectionCreated();

        URI uri = URI.create(targetUrl);
        URL url = uri.toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 配置连接参数
        connection.setRequestMethod(httpMethod);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setUseCaches(false);

        // 设置超时
        connection.setConnectTimeout(poolConfig.getConnectTimeout());
        connection.setReadTimeout(poolConfig.getReadTimeout());

        // 设置连接池相关属性
        if (poolConfig.isKeepAlive()) {
            connection.setRequestProperty("Connection", "keep-alive");
        }

        return connection;
    }

    /**
     * 设置请求头
     */
    private void setupRequestHeaders(HttpURLConnection connection, AsyncServerWebExchange exchange) {
        try {
            // 尝试从exchange获取请求信息
            // 由于AsyncServerWebExchange可能有不同的实现，我们需要安全地获取头信息

            // 设置默认Content-Type
            connection.setRequestProperty("Content-Type", "application/json");

            // 设置User-Agent
            connection.setRequestProperty("User-Agent", "YOP-AsyncHttpBackend/2.0");

            // 设置自定义头
            if (poolConfig.getCustomHeaders() != null) {
                for (Map.Entry<String, String> header : poolConfig.getCustomHeaders().entrySet()) {
                    connection.setRequestProperty(header.getKey(), header.getValue());
                }
            }

            // 尝试从属性中获取头信息
            @SuppressWarnings("unchecked")
            Map<String, String> headers = exchange.getAttribute("headers");
            if (headers != null) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    connection.setRequestProperty(header.getKey(), header.getValue());
                }
            }
        } catch (Exception e) {
            log.log(Level.WARNING, "Failed to setup request headers", e);
        }
    }

    /**
     * 发送请求体
     */
    private void sendRequestBody(HttpURLConnection connection, AsyncServerWebExchange exchange) throws Exception {
        // 从exchange属性中获取请求体
        byte[] requestBody = getRequestBodyFromExchange(exchange);

        if (requestBody.length > 0) {
            connection.setRequestProperty("Content-Length", String.valueOf(requestBody.length));

            try (OutputStream os = connection.getOutputStream()) {
                os.write(requestBody);
                os.flush();
            }
        }
    }

    /**
     * 从exchange中获取请求体
     */
    private byte[] getRequestBodyFromExchange(AsyncServerWebExchange exchange) {
        try {
            // 尝试从属性中获取请求体
            byte[] body = exchange.getAttribute("requestBody");
            if (body != null) {
                return body;
            }

            // 尝试从字符串属性获取
            String bodyStr = exchange.getAttribute("requestBodyString");
            if (bodyStr != null) {
                return bodyStr.getBytes("UTF-8");
            }

            // 默认返回空数组
            return new byte[0];
        } catch (Exception e) {
            log.log(Level.WARNING, "Failed to get request body from exchange", e);
            return new byte[0];
        }
    }

    /**
     * 读取响应
     */
    private Object readResponse(HttpURLConnection connection) throws Exception {
        int responseCode = connection.getResponseCode();
        
        InputStream inputStream;
        if (responseCode >= 200 && responseCode < 300) {
            inputStream = connection.getInputStream();
        } else {
            inputStream = connection.getErrorStream();
            if (inputStream == null) {
                inputStream = connection.getInputStream();
            }
        }
        
        if (inputStream == null) {
            throw new RuntimeException("No response stream available, response code: " + responseCode);
        }
        
        // 读取响应体
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        String responseBody = buffer.toString("UTF-8");
        
        // 检查响应状态
        if (responseCode >= 400) {
            throw new RuntimeException("HTTP error " + responseCode + ": " + responseBody);
        }
        
        log.fine("HTTP response received: " + responseCode + ", body length: " + responseBody.length());
        
        return responseBody;
    }

    @Override
    protected boolean doHealthCheck() {
        try {
            HttpURLConnection connection = createHttpConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            log.log(Level.WARNING, "HTTP health check failed for " + targetUrl, e);
            return false;
        }
    }

    @Override
    public BackendType getBackendType() {
        return BackendType.HTTP;
    }

    @Override
    public boolean supportsHealthCheck() {
        return true;
    }

    @Override
    public boolean supportsRetry() {
        return true;
    }

    @Override
    public int getRetryCount() {
        return poolConfig.getRetryCount();
    }

    @Override
    public boolean supportsCircuitBreaker() {
        return true;
    }

    @Override
    public long getTimeoutMillis() {
        return poolConfig.getReadTimeout();
    }

    /**
     * 获取连接池统计信息
     */
    public ConnectionPoolStats getPoolStats() {
        return poolStats;
    }

    // Getters and Setters
    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public HttpConnectionPoolConfig getPoolConfig() {
        return poolConfig;
    }

    public void setPoolConfig(HttpConnectionPoolConfig poolConfig) {
        this.poolConfig = poolConfig;
    }

    @Override
    public void close() throws Exception {
        super.close();
        // HTTP后端特定的清理逻辑
        log.info("AsyncHttpBackend closed: " + getName());
    }

    /**
     * HTTP连接池配置
     */
    public static class HttpConnectionPoolConfig {
        private int maxConnections = 100;           // 最大连接数
        private int connectTimeout = 5000;          // 连接超时(ms)
        private int readTimeout = 30000;            // 读超时(ms)
        private boolean keepAlive = true;           // 是否保持连接
        private int retryCount = 3;                 // 重试次数
        private Map<String, String> customHeaders;  // 自定义头

        // Getters and Setters
        public int getMaxConnections() { return maxConnections; }
        public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }
        
        public int getConnectTimeout() { return connectTimeout; }
        public void setConnectTimeout(int connectTimeout) { this.connectTimeout = connectTimeout; }
        
        public int getReadTimeout() { return readTimeout; }
        public void setReadTimeout(int readTimeout) { this.readTimeout = readTimeout; }
        
        public boolean isKeepAlive() { return keepAlive; }
        public void setKeepAlive(boolean keepAlive) { this.keepAlive = keepAlive; }
        
        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
        
        public Map<String, String> getCustomHeaders() { return customHeaders; }
        public void setCustomHeaders(Map<String, String> customHeaders) { this.customHeaders = customHeaders; }
    }

    /**
     * 连接池统计信息
     */
    public static class ConnectionPoolStats {
        private volatile long connectionsCreated = 0;
        private volatile long connectionsUsed = 0;
        private volatile long lastUsedTime = System.currentTimeMillis();

        public void recordConnectionCreated() {
            connectionsCreated++;
        }

        public void recordConnectionUsed() {
            connectionsUsed++;
            lastUsedTime = System.currentTimeMillis();
        }

        // Getters
        public long getConnectionsCreated() { return connectionsCreated; }
        public long getConnectionsUsed() { return connectionsUsed; }
        public long getLastUsedTime() { return lastUsedTime; }
    }
}