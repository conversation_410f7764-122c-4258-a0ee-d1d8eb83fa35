/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: YOP网关内存优化使用示例<br/>
 * description: 展示如何配置和使用YOP网关内存优化功能<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class YopGatewayMemoryOptimizationExample {

    private static final Logger log = Logger.getLogger(YopGatewayMemoryOptimizationExample.class.getName());

    public static void main(String[] args) {
        try {
            // 示例1: 使用默认配置初始化
            demonstrateDefaultConfiguration();
            
            // 示例2: 使用自定义配置初始化
            demonstrateCustomConfiguration();
            
            // 示例3: 演示优化的网关处理器使用
            demonstrateOptimizedHandler();
            
            // 示例4: 演示性能监控
            demonstratePerformanceMonitoring();
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "示例执行失败", e);
        }
    }

    /**
     * 示例1: 使用默认配置初始化
     */
    private static void demonstrateDefaultConfiguration() {
        log.info("=== 示例1: 默认配置初始化 ===");
        
        // 使用默认配置初始化
        YopGatewayMemoryOptimizationConfig config = YopGatewayMemoryOptimizationConfig.getInstance();
        
        log.info("默认配置已初始化");
        log.info("业务线程数: " + config.getConfig().getBusinessThreads());
        log.info("IO线程数: " + config.getConfig().getIoThreads());
        log.info("堆内存大小: " + config.getConfig().getHeapSizeMb() + "MB");
        log.info("直接内存大小: " + config.getConfig().getDirectMemoryMb() + "MB");
        
        // 获取优化状态
        YopGatewayMemoryOptimizationConfig.OptimizationStatus status = config.getOptimizationStatus();
        log.info("当前优化状态: " + status);
    }

    /**
     * 示例2: 使用自定义配置初始化
     */
    private static void demonstrateCustomConfiguration() {
        log.info("\n=== 示例2: 自定义配置初始化 ===");
        
        // 创建自定义配置
        YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig customConfig = 
            new YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig();
        
        // 配置参数
        customConfig.setBusinessThreads(64);           // 64个业务线程
        customConfig.setIoThreads(16);                 // 16个IO线程
        customConfig.setHeapSizeMb(2048);              // 2GB堆内存
        customConfig.setDirectMemoryMb(1024);          // 1GB直接内存
        customConfig.setCacheSizeMb(512);              // 512MB缓存
        customConfig.setEnableZeroCopy(true);          // 启用零拷贝
        customConfig.setEnableObjectPooling(true);     // 启用对象池
        customConfig.setEnableOffHeapCache(true);      // 启用堆外缓存
        customConfig.setEnableMemoryMonitoring(true);  // 启用内存监控
        
        log.info("自定义配置: " + customConfig);
        
        // 使用自定义配置初始化
        YopGatewayMemoryOptimizationConfig optimizationConfig = 
            YopGatewayMemoryOptimizationConfig.initialize(customConfig);
        
        log.info("自定义配置已初始化");
        
        // 获取组件实例
        OffHeapMemoryPoolManager memoryPool = optimizationConfig.getMemoryPoolManager();
        ObjectPoolManager objectPool = optimizationConfig.getObjectPoolManager();
        OffHeapCacheManager cacheManager = optimizationConfig.getCacheManager();
        ZeroCopyDataTransfer dataTransfer = optimizationConfig.getDataTransfer();
        
        log.info("所有优化组件已初始化完成");
    }

    /**
     * 示例3: 演示优化的网关处理器使用
     */
    private static void demonstrateOptimizedHandler() {
        log.info("\n=== 示例3: 优化网关处理器使用 ===");
        
        try {
            // 获取配置实例
            YopGatewayMemoryOptimizationConfig config = YopGatewayMemoryOptimizationConfig.getInstance();
            
            // 创建优化的处理器
            OptimizedYopGatewayHandler handler = new OptimizedYopGatewayHandler(config.getBusinessExecutor());
            
            // 创建测试请求
            OptimizedYopGatewayHandler.OptimizedHttpRequest request = createTestRequest();
            
            // 处理请求
            log.info("开始处理测试请求...");
            long startTime = System.currentTimeMillis();
            
            CompletableFuture<OptimizedYopGatewayHandler.OptimizedHttpResponse> future = 
                handler.handleRequest(request);
            
            OptimizedYopGatewayHandler.OptimizedHttpResponse response = future.get();
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            log.info("请求处理完成");
            log.info("响应状态: " + response.getStatus());
            log.info("处理时间: " + processingTime + "ms");
            log.info("处理器指标: " + handler.getMetrics());
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "处理器演示失败", e);
        }
    }

    /**
     * 示例4: 演示性能监控
     */
    private static void demonstratePerformanceMonitoring() {
        log.info("\n=== 示例4: 性能监控演示 ===");
        
        try {
            // 获取配置实例
            YopGatewayMemoryOptimizationConfig config = YopGatewayMemoryOptimizationConfig.getInstance();
            
            // 获取各个组件的监控信息
            demonstrateMemoryPoolMonitoring(config);
            demonstrateObjectPoolMonitoring(config);
            demonstrateCacheMonitoring(config);
            demonstrateOverallStatus(config);
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "性能监控演示失败", e);
        }
    }

    /**
     * 演示内存池监控
     */
    private static void demonstrateMemoryPoolMonitoring(YopGatewayMemoryOptimizationConfig config) {
        log.info("--- 内存池监控 ---");
        
        OffHeapMemoryPoolManager memoryPool = config.getMemoryPoolManager();
        
        // 分配一些内存来演示监控
        java.nio.ByteBuffer buffer1 = memoryPool.allocateDirectBuffer(1024);
        java.nio.ByteBuffer buffer2 = memoryPool.allocateDirectBuffer(2048);
        java.nio.ByteBuffer buffer3 = memoryPool.allocateDirectBuffer(4096);
        
        // 获取监控统计
        OffHeapMemoryPoolManager.PoolStatistics stats = memoryPool.getStatistics();
        log.info("内存池统计: " + stats);
        
        // 清理
        // 注意：在实际使用中，直接内存会在GC时自动清理
    }

    /**
     * 演示对象池监控
     */
    private static void demonstrateObjectPoolMonitoring(YopGatewayMemoryOptimizationConfig config) {
        log.info("--- 对象池监控 ---");
        
        ObjectPoolManager objectPool = config.getObjectPoolManager();
        
        // 使用对象池
        StringBuilder sb1 = objectPool.getStringBuilder();
        StringBuilder sb2 = objectPool.getStringBuilder();
        byte[] bytes1 = objectPool.getSmallByteArray();
        byte[] bytes2 = objectPool.getMediumByteArray();
        
        // 获取监控统计
        ObjectPoolManager.PoolStatistics stats = objectPool.getStatistics();
        log.info("对象池统计: " + stats);
        
        // 归还对象
        objectPool.returnStringBuilder(sb1);
        objectPool.returnStringBuilder(sb2);
        objectPool.returnSmallByteArray(bytes1);
        objectPool.returnMediumByteArray(bytes2);
    }

    /**
     * 演示缓存监控
     */
    private static void demonstrateCacheMonitoring(YopGatewayMemoryOptimizationConfig config) {
        log.info("--- 缓存监控 ---");
        
        OffHeapCacheManager cacheManager = config.getCacheManager();
        
        // 添加一些缓存数据
        cacheManager.put("key1", "value1".getBytes(), 60000);
        cacheManager.put("key2", "value2".getBytes(), 60000);
        cacheManager.put("key3", "value3".getBytes(), 60000);
        
        // 执行一些查询
        cacheManager.get("key1");
        cacheManager.get("key2");
        cacheManager.get("key4"); // 缓存未命中
        
        // 获取监控统计
        OffHeapCacheManager.CacheStatistics stats = cacheManager.getStatistics();
        log.info("缓存统计: " + stats);
    }

    /**
     * 演示整体状态监控
     */
    private static void demonstrateOverallStatus(YopGatewayMemoryOptimizationConfig config) {
        log.info("--- 整体状态监控 ---");
        
        // 获取整体优化状态
        YopGatewayMemoryOptimizationConfig.OptimizationStatus status = config.getOptimizationStatus();
        log.info("整体优化状态: " + status);
        
        // 获取JVM内存信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        log.info("JVM内存信息:");
        log.info("  总内存: " + formatBytes(totalMemory));
        log.info("  已用内存: " + formatBytes(usedMemory));
        log.info("  空闲内存: " + formatBytes(freeMemory));
        log.info("  最大内存: " + formatBytes(maxMemory));
        log.info("  内存使用率: " + String.format("%.2f%%", (double) usedMemory / totalMemory * 100));
    }

    /**
     * 创建测试请求
     */
    private static OptimizedYopGatewayHandler.OptimizedHttpRequest createTestRequest() {
        OptimizedYopGatewayHandler.OptimizedHttpRequest request = 
            new OptimizedYopGatewayHandler.OptimizedHttpRequest();
        
        request.setMethod("POST");
        request.setUri("/api/payment/process");
        
        // 设置请求头
        java.util.Map<String, String> headers = new java.util.HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("User-Agent", "YopGateway/1.0");
        request.setHeaders(headers);
        
        // 设置请求体
        String jsonBody = "{\n" +
                         "  \"amount\": 100.00,\n" +
                         "  \"currency\": \"CNY\",\n" +
                         "  \"merchantId\": \"test123\",\n" +
                         "  \"orderId\": \"order_" + System.currentTimeMillis() + "\",\n" +
                         "  \"description\": \"Test payment request\"\n" +
                         "}";
        
        java.nio.ByteBuffer bodyBuffer = java.nio.ByteBuffer.wrap(jsonBody.getBytes());
        request.setBodyBuffer(bodyBuffer);
        
        return request;
    }

    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 演示配置不同的优化策略
     */
    public static void demonstrateOptimizationStrategies() {
        log.info("\n=== 优化策略配置示例 ===");
        
        // 高吞吐量配置（适用于处理大量小请求）
        YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig highThroughputConfig = 
            new YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig();
        highThroughputConfig.setBusinessThreads(128);
        highThroughputConfig.setIoThreads(32);
        highThroughputConfig.setHeapSizeMb(4096);
        highThroughputConfig.setDirectMemoryMb(2048);
        highThroughputConfig.setCacheSizeMb(1024);
        highThroughputConfig.setEnableObjectPooling(true);
        log.info("高吞吐量配置: " + highThroughputConfig);
        
        // 低延迟配置（适用于对延迟敏感的场景）
        YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig lowLatencyConfig = 
            new YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig();
        lowLatencyConfig.setBusinessThreads(32);
        lowLatencyConfig.setIoThreads(8);
        lowLatencyConfig.setHeapSizeMb(1024);
        lowLatencyConfig.setDirectMemoryMb(512);
        lowLatencyConfig.setCacheSizeMb(256);
        lowLatencyConfig.setEnableZeroCopy(true);
        lowLatencyConfig.setEnableOffHeapCache(true);
        log.info("低延迟配置: " + lowLatencyConfig);
        
        // 内存优化配置（适用于内存受限的环境）
        YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig memoryOptimizedConfig = 
            new YopGatewayMemoryOptimizationConfig.MemoryOptimizationConfig();
        memoryOptimizedConfig.setBusinessThreads(16);
        memoryOptimizedConfig.setIoThreads(4);
        memoryOptimizedConfig.setHeapSizeMb(512);
        memoryOptimizedConfig.setDirectMemoryMb(256);
        memoryOptimizedConfig.setCacheSizeMb(128);
        memoryOptimizedConfig.setEnableMemoryMonitoring(true);
        log.info("内存优化配置: " + memoryOptimizedConfig);
    }

    /**
     * 演示最佳实践建议
     */
    public static void demonstrateBestPractices() {
        log.info("\n=== 最佳实践建议 ===");
        
        log.info("1. JVM启动参数建议:");
        log.info("   -server");
        log.info("   -Xms2g -Xmx2g");
        log.info("   -XX:+UseG1GC");
        log.info("   -XX:MaxGCPauseMillis=20");
        log.info("   -XX:+UnlockExperimentalVMOptions");
        log.info("   -XX:+UseStringDeduplication");
        log.info("   -XX:MaxDirectMemorySize=1g");
        
        log.info("\n2. 监控指标关注点:");
        log.info("   - 堆内存使用率 < 85%");
        log.info("   - GC暂停时间 < 20ms");
        log.info("   - 直接内存使用率 < 80%");
        log.info("   - 缓存命中率 > 80%");
        log.info("   - 对象池利用率 > 60%");
        
        log.info("\n3. 性能调优建议:");
        log.info("   - 根据实际负载调整线程池大小");
        log.info("   - 定期监控内存使用情况");
        log.info("   - 合理设置缓存大小和过期时间");
        log.info("   - 启用零拷贝减少内存拷贝");
        log.info("   - 使用对象池避免频繁对象创建");
    }
}