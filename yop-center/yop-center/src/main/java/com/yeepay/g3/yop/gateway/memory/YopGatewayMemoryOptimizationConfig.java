/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: YOP网关内存优化配置类<br/>
 * description: 统一管理和配置所有内存优化组件，提供一键式初始化和配置<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class YopGatewayMemoryOptimizationConfig {

    private static final Logger log = Logger.getLogger(YopGatewayMemoryOptimizationConfig.class.getName());

    // 默认配置参数
    private static final int DEFAULT_BUSINESS_THREADS = Runtime.getRuntime().availableProcessors() * 2;
    private static final int DEFAULT_IO_THREADS = Runtime.getRuntime().availableProcessors();
    private static final long DEFAULT_HEAP_SIZE_MB = 1024;
    private static final long DEFAULT_DIRECT_MEMORY_MB = 512;
    private static final long DEFAULT_CACHE_SIZE_MB = 256;

    // 优化组件实例
    private OffHeapMemoryPoolManager memoryPoolManager;
    private ObjectPoolManager objectPoolManager;
    private OffHeapCacheManager cacheManager;
    private ZeroCopyDataTransfer dataTransfer;
    private MemoryMonitoringService monitoringService;
    private GCOptimizationConfig gcOptimizationConfig;

    // 线程池
    private ExecutorService businessExecutor;
    private ExecutorService ioExecutor;
    private ScheduledExecutorService scheduledExecutor;

    // 配置参数
    private final MemoryOptimizationConfig config;

    // 单例实例
    private static volatile YopGatewayMemoryOptimizationConfig instance;

    /**
     * 私有构造函数
     */
    private YopGatewayMemoryOptimizationConfig(MemoryOptimizationConfig config) {
        this.config = config != null ? config : createDefaultConfig();
        initializeComponents();
    }

    /**
     * 获取单例实例
     */
    public static YopGatewayMemoryOptimizationConfig getInstance() {
        if (instance == null) {
            synchronized (YopGatewayMemoryOptimizationConfig.class) {
                if (instance == null) {
                    instance = new YopGatewayMemoryOptimizationConfig(null);
                }
            }
        }
        return instance;
    }

    /**
     * 使用自定义配置初始化
     */
    public static YopGatewayMemoryOptimizationConfig initialize(MemoryOptimizationConfig config) {
        synchronized (YopGatewayMemoryOptimizationConfig.class) {
            if (instance != null) {
                log.warning("MemoryOptimizationConfig already initialized, shutting down previous instance");
                instance.shutdown();
            }
            instance = new YopGatewayMemoryOptimizationConfig(config);
        }
        return instance;
    }

    /**
     * 初始化所有组件
     */
    private void initializeComponents() {
        log.info("开始初始化YOP网关内存优化组件...");

        try {
            // 1. 初始化线程池
            initializeThreadPools();

            // 2. 初始化内存管理组件
            initializeMemoryComponents();

            // 3. 初始化GC优化配置
            initializeGCOptimization();

            // 4. 启动监控服务
            startMonitoringServices();

            // 5. 注册关闭钩子
            registerShutdownHook();

            log.info("YOP网关内存优化组件初始化完成");

        } catch (Exception e) {
            log.log(Level.SEVERE, "内存优化组件初始化失败", e);
            throw new RuntimeException("Memory optimization initialization failed", e);
        }
    }

    /**
     * 初始化线程池
     */
    private void initializeThreadPools() {
        log.info("初始化线程池...");

        // 业务处理线程池
        this.businessExecutor = Executors.newFixedThreadPool(
            config.getBusinessThreads(),
            r -> {
                Thread t = new Thread(r, "yop-business-" + System.currentTimeMillis());
                t.setDaemon(false);
                return t;
            }
        );

        // IO处理线程池
        this.ioExecutor = Executors.newFixedThreadPool(
            config.getIoThreads(),
            r -> {
                Thread t = new Thread(r, "yop-io-" + System.currentTimeMillis());
                t.setDaemon(false);
                return t;
            }
        );

        // 调度线程池
        this.scheduledExecutor = Executors.newScheduledThreadPool(
            2,
            r -> {
                Thread t = new Thread(r, "yop-scheduler-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            }
        );

        log.info("线程池初始化完成 - 业务线程:" + config.getBusinessThreads() + 
                ", IO线程:" + config.getIoThreads());
    }

    /**
     * 初始化内存管理组件
     */
    private void initializeMemoryComponents() {
        log.info("初始化内存管理组件...");

        // 初始化堆外内存池管理器
        this.memoryPoolManager = OffHeapMemoryPoolManager.getInstance();
        
        // 初始化对象池管理器
        this.objectPoolManager = ObjectPoolManager.getInstance();
        
        // 初始化堆外缓存管理器
        this.cacheManager = OffHeapCacheManager.getInstance();
        
        // 初始化零拷贝数据传输器
        this.dataTransfer = ZeroCopyDataTransfer.getInstance();

        log.info("内存管理组件初始化完成");
    }

    /**
     * 初始化GC优化配置
     */
    private void initializeGCOptimization() {
        log.info("初始化GC优化配置...");

        this.gcOptimizationConfig = GCOptimizationConfig.getInstance();
        
        // 生成基本的GC优化建议
        String gcAdvice = "建议JVM参数: -Xms" + config.getHeapSizeMb() + "m " +
                         "-Xmx" + config.getHeapSizeMb() + "m " +
                         "-XX:MaxDirectMemorySize=" + config.getDirectMemoryMb() + "m " +
                         "-XX:+UseG1GC -XX:MaxGCPauseMillis=20";
        
        log.info("GC优化配置完成，" + gcAdvice);
    }

    /**
     * 启动监控服务
     */
    private void startMonitoringServices() {
        log.info("启动内存监控服务...");

        // 初始化监控服务
        this.monitoringService = MemoryMonitoringService.getInstance();

        // 定期执行内存监控和报告
        scheduledExecutor.scheduleAtFixedRate(
            this::performMemoryCheck,
            10, // 初始延迟10秒
            30, // 每30秒执行一次
            TimeUnit.SECONDS
        );

        // 定期清理过期缓存
        scheduledExecutor.scheduleAtFixedRate(
            this::performCacheCleanup,
            60, // 初始延迟1分钟
            300, // 每5分钟执行一次
            TimeUnit.SECONDS
        );

        log.info("内存监控服务启动完成");
    }

    /**
     * 执行内存检查
     */
    private void performMemoryCheck() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double usagePercent = (double) usedMemory / totalMemory * 100;
            
            // 检查内存使用率是否过高
            if (usagePercent > 85.0) {
                log.warning("堆内存使用率过高: " + String.format("%.2f%%", usagePercent));
                
                // 触发内存优化措施
                performMemoryOptimization();
            }
            
            log.fine("内存检查完成 - 使用率: " + String.format("%.2f%%", usagePercent));
            
        } catch (Exception e) {
            log.log(Level.WARNING, "内存检查执行失败", e);
        }
    }

    /**
     * 执行缓存清理
     */
    private void performCacheCleanup() {
        try {
            // 简化缓存清理实现
            log.fine("执行缓存清理");
            
            // 清理线程本地缓存
            memoryPoolManager.clearThreadLocalCaches();
            
            log.fine("定期缓存清理完成");
            
        } catch (Exception e) {
            log.log(Level.WARNING, "缓存清理执行失败", e);
        }
    }

    /**
     * 执行内存优化
     */
    private void performMemoryOptimization() {
        try {
            log.info("执行内存优化措施...");
            
            // 1. 强制执行GC
            System.gc();
            
            // 2. 清理线程本地缓存
            memoryPoolManager.clearThreadLocalCaches();
            
            log.info("内存优化措施执行完成");
            
        } catch (Exception e) {
            log.log(Level.WARNING, "内存优化执行失败", e);
        }
    }

    /**
     * 注册关闭钩子
     */
    private void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown, "yop-memory-shutdown"));
    }

    /**
     * 关闭所有组件
     */
    public void shutdown() {
        log.info("开始关闭YOP网关内存优化组件...");

        try {
            // 1. 关闭调度器
            if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
                scheduledExecutor.shutdown();
                scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS);
            }

            // 2. 关闭线程池
            if (businessExecutor != null && !businessExecutor.isShutdown()) {
                businessExecutor.shutdown();
                businessExecutor.awaitTermination(10, TimeUnit.SECONDS);
            }

            if (ioExecutor != null && !ioExecutor.isShutdown()) {
                ioExecutor.shutdown();
                ioExecutor.awaitTermination(5, TimeUnit.SECONDS);
            }

            // 3. 清理内存组件
            if (memoryPoolManager != null) {
                memoryPoolManager.clearThreadLocalCaches();
            }

            log.info("YOP网关内存优化组件关闭完成");

        } catch (Exception e) {
            log.log(Level.WARNING, "组件关闭过程中发生错误", e);
        }
    }

    /**
     * 创建默认配置
     */
    private static MemoryOptimizationConfig createDefaultConfig() {
        MemoryOptimizationConfig config = new MemoryOptimizationConfig();
        config.setBusinessThreads(DEFAULT_BUSINESS_THREADS);
        config.setIoThreads(DEFAULT_IO_THREADS);
        config.setHeapSizeMb(DEFAULT_HEAP_SIZE_MB);
        config.setDirectMemoryMb(DEFAULT_DIRECT_MEMORY_MB);
        config.setCacheSizeMb(DEFAULT_CACHE_SIZE_MB);
        return config;
    }

    // Getters
    public OffHeapMemoryPoolManager getMemoryPoolManager() { return memoryPoolManager; }
    public ObjectPoolManager getObjectPoolManager() { return objectPoolManager; }
    public OffHeapCacheManager getCacheManager() { return cacheManager; }
    public ZeroCopyDataTransfer getDataTransfer() { return dataTransfer; }
    public MemoryMonitoringService getMonitoringService() { return monitoringService; }
    public GCOptimizationConfig getGcOptimizationConfig() { return gcOptimizationConfig; }
    public ExecutorService getBusinessExecutor() { return businessExecutor; }
    public ExecutorService getIoExecutor() { return ioExecutor; }
    public MemoryOptimizationConfig getConfig() { return config; }

    /**
     * 内存优化配置类
     */
    public static class MemoryOptimizationConfig {
        private int businessThreads = DEFAULT_BUSINESS_THREADS;
        private int ioThreads = DEFAULT_IO_THREADS;
        private long heapSizeMb = DEFAULT_HEAP_SIZE_MB;
        private long directMemoryMb = DEFAULT_DIRECT_MEMORY_MB;
        private long cacheSizeMb = DEFAULT_CACHE_SIZE_MB;
        private boolean enableZeroCopy = true;
        private boolean enableObjectPooling = true;
        private boolean enableOffHeapCache = true;
        private boolean enableMemoryMonitoring = true;

        // Getters and Setters
        public int getBusinessThreads() { return businessThreads; }
        public void setBusinessThreads(int businessThreads) { this.businessThreads = businessThreads; }

        public int getIoThreads() { return ioThreads; }
        public void setIoThreads(int ioThreads) { this.ioThreads = ioThreads; }

        public long getHeapSizeMb() { return heapSizeMb; }
        public void setHeapSizeMb(long heapSizeMb) { this.heapSizeMb = heapSizeMb; }

        public long getDirectMemoryMb() { return directMemoryMb; }
        public void setDirectMemoryMb(long directMemoryMb) { this.directMemoryMb = directMemoryMb; }

        public long getCacheSizeMb() { return cacheSizeMb; }
        public void setCacheSizeMb(long cacheSizeMb) { this.cacheSizeMb = cacheSizeMb; }

        public boolean isEnableZeroCopy() { return enableZeroCopy; }
        public void setEnableZeroCopy(boolean enableZeroCopy) { this.enableZeroCopy = enableZeroCopy; }

        public boolean isEnableObjectPooling() { return enableObjectPooling; }
        public void setEnableObjectPooling(boolean enableObjectPooling) { this.enableObjectPooling = enableObjectPooling; }

        public boolean isEnableOffHeapCache() { return enableOffHeapCache; }
        public void setEnableOffHeapCache(boolean enableOffHeapCache) { this.enableOffHeapCache = enableOffHeapCache; }

        public boolean isEnableMemoryMonitoring() { return enableMemoryMonitoring; }
        public void setEnableMemoryMonitoring(boolean enableMemoryMonitoring) { this.enableMemoryMonitoring = enableMemoryMonitoring; }

        @Override
        public String toString() {
            return String.format("MemoryOptimizationConfig{businessThreads=%d, ioThreads=%d, heapSizeMb=%d, directMemoryMb=%d, cacheSizeMb=%d, enableZeroCopy=%s, enableObjectPooling=%s, enableOffHeapCache=%s, enableMemoryMonitoring=%s}",
                               businessThreads, ioThreads, heapSizeMb, directMemoryMb, cacheSizeMb, enableZeroCopy, enableObjectPooling, enableOffHeapCache, enableMemoryMonitoring);
        }
    }

    /**
     * 获取当前优化状态
     */
    public OptimizationStatus getOptimizationStatus() {
        OptimizationStatus status = new OptimizationStatus();
        
        try {
            // 获取内存状态
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            status.heapUsagePercent = (double) usedMemory / totalMemory * 100;
            status.directMemoryUsedMb = 0; // 简化实现
            status.cacheHitRate = 0.0; // 简化实现
            
            // 获取线程池状态
            status.businessThreadsActive = businessExecutor instanceof java.util.concurrent.ThreadPoolExecutor ?
                ((java.util.concurrent.ThreadPoolExecutor) businessExecutor).getActiveCount() : -1;
            
            status.optimizationEnabled = true;
            status.lastUpdateTime = System.currentTimeMillis();
            
        } catch (Exception e) {
            log.log(Level.WARNING, "获取优化状态失败", e);
            status.optimizationEnabled = false;
        }
        
        return status;
    }

    /**
     * 优化状态类
     */
    public static class OptimizationStatus {
        public double heapUsagePercent;
        public long directMemoryUsedMb;
        public double cacheHitRate;
        public int businessThreadsActive;
        public boolean optimizationEnabled;
        public long lastUpdateTime;

        @Override
        public String toString() {
            return String.format("OptimizationStatus{heapUsage=%.2f%%, directMemory=%dMB, cacheHitRate=%.2f%%, activeThreads=%d, enabled=%s, lastUpdate=%d}",
                               heapUsagePercent, directMemoryUsedMb, cacheHitRate, businessThreadsActive, optimizationEnabled, lastUpdateTime);
        }
    }
}