/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.logging.Logger;
import java.util.logging.Level;
import javax.management.NotificationEmitter;
import javax.management.NotificationListener;
import javax.management.Notification;

/**
 * title: GC优化管理器<br/>
 * description: 提供GC性能监控和优化建议，配置低延迟GC策略<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class GCOptimizationManager {

    private static final Logger log = Logger.getLogger(GCOptimizationManager.class.getName());
    private static final GCOptimizationManager INSTANCE = new GCOptimizationManager();

    // GC监控
    private final GCMetrics gcMetrics = new GCMetrics();
    
    // 内存监控
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    
    // 调度器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2, 
        r -> {
            Thread t = new Thread(r, "gc-optimization-monitor");
            t.setDaemon(true);
            return t;
        });

    // 配置
    private final GCConfig config = new GCConfig();
    
    // 性能目标
    private static final long TARGET_GC_PAUSE_MS = 20; // 目标GC暂停时间 < 20ms
    private static final double TARGET_GC_FREQUENCY = 1.0; // 目标Young GC频率 < 1次/秒
    private static final double HEAP_USAGE_WARNING_THRESHOLD = 0.8; // 堆使用率告警阈值 80%

    private GCOptimizationManager() {
        log.info("GCOptimizationManager initialized");
        setupGCMonitoring();
        startOptimizationMonitoring();
    }

    public static GCOptimizationManager getInstance() {
        return INSTANCE;
    }

    /**
     * 设置GC监控
     */
    private void setupGCMonitoring() {
        for (GarbageCollectorMXBean gcBean : ManagementFactory.getGarbageCollectorMXBeans()) {
            if (gcBean instanceof NotificationEmitter) {
                NotificationEmitter emitter = (NotificationEmitter) gcBean;
                NotificationListener listener = new GCNotificationListener(gcBean.getName());
                emitter.addNotificationListener(listener, null, null);
                log.info("Added GC listener for: " + gcBean.getName());
            }
        }
    }

    /**
     * 启动优化监控
     */
    private void startOptimizationMonitoring() {
        // 定期检查GC性能
        scheduler.scheduleAtFixedRate(this::checkGCPerformance, 30, 30, TimeUnit.SECONDS);
        
        // 定期检查内存使用情况
        scheduler.scheduleAtFixedRate(this::checkMemoryUsage, 10, 10, TimeUnit.SECONDS);
        
        // 定期输出性能报告
        scheduler.scheduleAtFixedRate(this::generatePerformanceReport, 300, 300, TimeUnit.SECONDS);
    }

    /**
     * 检查GC性能
     */
    private void checkGCPerformance() {
        try {
            // 检查GC暂停时间
            double avgPauseTime = gcMetrics.getAverageGCPauseTime();
            if (avgPauseTime > TARGET_GC_PAUSE_MS) {
                log.warning(String.format("GC pause time exceeded target: %.2fms > %dms", 
                           avgPauseTime, TARGET_GC_PAUSE_MS));
                suggestGCTuning();
            }

            // 检查GC频率
            double gcFrequency = gcMetrics.getYoungGCFrequency();
            if (gcFrequency > TARGET_GC_FREQUENCY) {
                log.warning(String.format("Young GC frequency exceeded target: %.2f/s > %.2f/s", 
                           gcFrequency, TARGET_GC_FREQUENCY));
                suggestHeapSizeTuning();
            }

            // 检查Full GC
            if (gcMetrics.getRecentFullGCCount() > 0) {
                log.severe("Full GC detected! Count: " + gcMetrics.getRecentFullGCCount());
                suggestFullGCOptimization();
            }

        } catch (Exception e) {
            log.log(Level.WARNING, "Error checking GC performance", e);
        }
    }

    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        try {
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            double heapUsageRatio = (double) heapUsage.getUsed() / heapUsage.getMax();
            
            if (heapUsageRatio > HEAP_USAGE_WARNING_THRESHOLD) {
                log.warning(String.format("High heap usage: %.2f%% (used: %dMB, max: %dMB)", 
                           heapUsageRatio * 100, 
                           heapUsage.getUsed() / 1024 / 1024,
                           heapUsage.getMax() / 1024 / 1024));
                
                if (heapUsageRatio > 0.95) {
                    suggestMemoryOptimization();
                }
            }

            // 记录堆外内存使用情况
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            gcMetrics.recordMemoryUsage(heapUsage.getUsed(), nonHeapUsage.getUsed());

        } catch (Exception e) {
            log.log(Level.WARNING, "Error checking memory usage", e);
        }
    }

    /**
     * 生成性能报告
     */
    private void generatePerformanceReport() {
        try {
            StringBuilder report = new StringBuilder("\n=== GC Performance Report ===\n");
            
            // GC统计
            report.append("GC Statistics:\n");
            report.append(String.format("  Young GC Count: %d\n", gcMetrics.getYoungGCCount()));
            report.append(String.format("  Old GC Count: %d\n", gcMetrics.getOldGCCount()));
            report.append(String.format("  Total GC Time: %.2f seconds\n", gcMetrics.getTotalGCTime() / 1000.0));
            report.append(String.format("  Average GC Pause: %.2f ms\n", gcMetrics.getAverageGCPauseTime()));
            report.append(String.format("  Young GC Frequency: %.2f /second\n", gcMetrics.getYoungGCFrequency()));
            
            // 内存统计
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            
            report.append("Memory Usage:\n");
            report.append(String.format("  Heap Used: %d MB / %d MB (%.2f%%)\n", 
                         heapUsage.getUsed() / 1024 / 1024,
                         heapUsage.getMax() / 1024 / 1024,
                         (double) heapUsage.getUsed() / heapUsage.getMax() * 100));
            report.append(String.format("  Non-Heap Used: %d MB / %d MB\n", 
                         nonHeapUsage.getUsed() / 1024 / 1024,
                         nonHeapUsage.getMax() != -1 ? nonHeapUsage.getMax() / 1024 / 1024 : -1));
            
            // 性能指标评估
            report.append("Performance Assessment:\n");
            report.append(String.format("  GC Pause Target: %s\n", 
                         gcMetrics.getAverageGCPauseTime() <= TARGET_GC_PAUSE_MS ? "✓ PASSED" : "✗ FAILED"));
            report.append(String.format("  GC Frequency Target: %s\n", 
                         gcMetrics.getYoungGCFrequency() <= TARGET_GC_FREQUENCY ? "✓ PASSED" : "✗ FAILED"));
            report.append(String.format("  Memory Usage: %s\n", 
                         (double) heapUsage.getUsed() / heapUsage.getMax() <= HEAP_USAGE_WARNING_THRESHOLD ? "✓ NORMAL" : "⚠ HIGH"));

            report.append("===========================\n");
            
            log.info(report.toString());

        } catch (Exception e) {
            log.log(Level.WARNING, "Error generating performance report", e);
        }
    }

    /**
     * 建议GC调优
     */
    private void suggestGCTuning() {
        StringBuilder suggestions = new StringBuilder("GC Tuning Suggestions:\n");
        
        suggestions.append("1. Consider using G1GC for low latency:\n");
        suggestions.append("   -XX:+UseG1GC\n");
        suggestions.append("   -XX:MaxGCPauseMillis=20\n");
        suggestions.append("   -XX:G1HeapRegionSize=16m\n");
        
        suggestions.append("2. Or consider ZGC for ultra-low latency (Java 11+):\n");
        suggestions.append("   -XX:+UnlockExperimentalVMOptions\n");
        suggestions.append("   -XX:+UseZGC\n");
        
        suggestions.append("3. Tune GC threads:\n");
        suggestions.append("   -XX:ParallelGCThreads=" + Math.max(1, Runtime.getRuntime().availableProcessors() / 2) + "\n");
        suggestions.append("   -XX:ConcGCThreads=" + Math.max(1, Runtime.getRuntime().availableProcessors() / 4) + "\n");

        log.info(suggestions.toString());
    }

    /**
     * 建议堆大小调优
     */
    private void suggestHeapSizeTuning() {
        StringBuilder suggestions = new StringBuilder("Heap Size Tuning Suggestions:\n");
        
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        long currentMaxHeap = heapUsage.getMax();
        long suggestedHeap = (long) (currentMaxHeap * 1.5);
        
        suggestions.append("1. Increase heap size:\n");
        suggestions.append(String.format("   -Xms%dm -Xmx%dm\n", 
                          suggestedHeap / 1024 / 1024, suggestedHeap / 1024 / 1024));
        
        suggestions.append("2. Optimize young generation:\n");
        suggestions.append("   -XX:NewRatio=3\n");
        suggestions.append("   -XX:SurvivorRatio=8\n");
        
        suggestions.append("3. Use off-heap storage for large objects\n");

        log.info(suggestions.toString());
    }

    /**
     * 建议Full GC优化
     */
    private void suggestFullGCOptimization() {
        StringBuilder suggestions = new StringBuilder("Full GC Optimization Suggestions:\n");
        
        suggestions.append("1. Increase heap size to avoid Full GC\n");
        suggestions.append("2. Tune old generation size:\n");
        suggestions.append("   -XX:NewRatio=2\n");
        
        suggestions.append("3. Enable concurrent collection:\n");
        suggestions.append("   -XX:+UseConcMarkSweepGC (if using CMS)\n");
        suggestions.append("   -XX:+CMSIncrementalMode\n");
        
        suggestions.append("4. Investigate memory leaks\n");
        suggestions.append("5. Review object lifecycle and caching strategies\n");

        log.severe(suggestions.toString());
    }

    /**
     * 建议内存优化
     */
    private void suggestMemoryOptimization() {
        StringBuilder suggestions = new StringBuilder("Memory Optimization Suggestions:\n");
        
        suggestions.append("1. Enable object pooling for frequently used objects\n");
        suggestions.append("2. Use off-heap storage for large data\n");
        suggestions.append("3. Review and optimize caching strategies\n");
        suggestions.append("4. Check for memory leaks\n");
        suggestions.append("5. Consider increasing heap size\n");

        log.warning(suggestions.toString());
    }

    /**
     * 获取GC指标
     */
    public GCMetrics getGCMetrics() {
        return gcMetrics;
    }

    /**
     * 获取推荐的JVM参数
     */
    public String getRecommendedJVMArgs() {
        StringBuilder args = new StringBuilder();
        
        // 基础GC配置
        args.append("-XX:+UseG1GC ");
        args.append("-XX:MaxGCPauseMillis=20 ");
        args.append("-XX:G1HeapRegionSize=16m ");
        
        // 堆大小配置
        long recommendedHeap = Math.max(2048, Runtime.getRuntime().maxMemory() / 1024 / 1024);
        args.append("-Xms").append(recommendedHeap).append("m ");
        args.append("-Xmx").append(recommendedHeap).append("m ");
        
        // 直接内存配置
        args.append("-XX:MaxDirectMemorySize=").append(recommendedHeap / 2).append("m ");
        
        // GC线程配置
        int processors = Runtime.getRuntime().availableProcessors();
        args.append("-XX:ParallelGCThreads=").append(Math.max(1, processors / 2)).append(" ");
        args.append("-XX:ConcGCThreads=").append(Math.max(1, processors / 4)).append(" ");
        
        // GC日志配置
        args.append("-XX:+PrintGC ");
        args.append("-XX:+PrintGCDetails ");
        args.append("-XX:+PrintGCTimeStamps ");
        args.append("-Xloggc:gc.log ");
        
        // 其他优化
        args.append("-XX:+UseCompressedOops ");
        args.append("-XX:+UseCompressedClassPointers ");
        
        return args.toString().trim();
    }

    /**
     * GC通知监听器
     */
    private class GCNotificationListener implements NotificationListener {
        private final String gcName;

        public GCNotificationListener(String gcName) {
            this.gcName = gcName;
        }

        @Override
        public void handleNotification(Notification notification, Object handback) {
            if ("com.sun.management.gc.notification".equals(notification.getType())) {
                // 解析GC通知并记录指标
                gcMetrics.recordGCEvent(gcName, System.currentTimeMillis());
            }
        }
    }

    /**
     * GC指标收集器
     */
    public static class GCMetrics {
        private final LongAdder youngGCCount = new LongAdder();
        private final LongAdder oldGCCount = new LongAdder();
        private final LongAdder totalGCTime = new LongAdder();
        private final AtomicLong lastGCTime = new AtomicLong(System.currentTimeMillis());
        private final AtomicLong monitoringStartTime = new AtomicLong(System.currentTimeMillis());
        private final LongAdder recentFullGCCount = new LongAdder();
        private final AtomicLong lastResetTime = new AtomicLong(System.currentTimeMillis());
        
        // 内存使用记录
        private final AtomicLong heapUsed = new AtomicLong();
        private final AtomicLong nonHeapUsed = new AtomicLong();

        public void recordGCEvent(String gcName, long timestamp) {
            if (gcName.toLowerCase().contains("young") || 
                gcName.toLowerCase().contains("parallel") ||
                gcName.toLowerCase().contains("g1 young")) {
                youngGCCount.increment();
            } else {
                oldGCCount.increment();
                recentFullGCCount.increment();
            }
            
            lastGCTime.set(timestamp);
        }

        public void recordMemoryUsage(long heap, long nonHeap) {
            heapUsed.set(heap);
            nonHeapUsed.set(nonHeap);
        }

        public long getYoungGCCount() {
            return youngGCCount.sum();
        }

        public long getOldGCCount() {
            return oldGCCount.sum();
        }

        public long getTotalGCTime() {
            return totalGCTime.sum();
        }

        public double getAverageGCPauseTime() {
            long totalGCs = youngGCCount.sum() + oldGCCount.sum();
            return totalGCs > 0 ? (double) totalGCTime.sum() / totalGCs : 0.0;
        }

        public double getYoungGCFrequency() {
            long elapsed = System.currentTimeMillis() - monitoringStartTime.get();
            return elapsed > 0 ? (double) youngGCCount.sum() / (elapsed / 1000.0) : 0.0;
        }

        public long getRecentFullGCCount() {
            long now = System.currentTimeMillis();
            if (now - lastResetTime.get() > 60000) { // 每分钟重置一次
                long count = recentFullGCCount.sum();
                recentFullGCCount.reset();
                lastResetTime.set(now);
                return count;
            }
            return recentFullGCCount.sum();
        }

        public long getHeapUsed() {
            return heapUsed.get();
        }

        public long getNonHeapUsed() {
            return nonHeapUsed.get();
        }
    }

    /**
     * GC配置
     */
    public static class GCConfig {
        private long monitoringIntervalMs = 30000;
        private long reportIntervalMs = 300000;
        private double heapUsageWarningThreshold = 0.8;
        private long targetGCPauseMs = 20;

        // Getters and Setters
        public long getMonitoringIntervalMs() { return monitoringIntervalMs; }
        public void setMonitoringIntervalMs(long monitoringIntervalMs) { this.monitoringIntervalMs = monitoringIntervalMs; }

        public long getReportIntervalMs() { return reportIntervalMs; }
        public void setReportIntervalMs(long reportIntervalMs) { this.reportIntervalMs = reportIntervalMs; }

        public double getHeapUsageWarningThreshold() { return heapUsageWarningThreshold; }
        public void setHeapUsageWarningThreshold(double heapUsageWarningThreshold) { this.heapUsageWarningThreshold = heapUsageWarningThreshold; }

        public long getTargetGCPauseMs() { return targetGCPauseMs; }
        public void setTargetGCPauseMs(long targetGCPauseMs) { this.targetGCPauseMs = targetGCPauseMs; }
    }

    /**
     * 优雅关闭
     */
    public void shutdown() {
        log.info("Shutting down GCOptimizationManager");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}