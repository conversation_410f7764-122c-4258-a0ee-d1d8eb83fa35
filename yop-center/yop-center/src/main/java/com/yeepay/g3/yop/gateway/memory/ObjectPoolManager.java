/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Supplier;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 对象池化管理器<br/>
 * description: 高性能对象池管理，减少频繁对象创建和GC压力<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class ObjectPoolManager {

    private static final Logger log = Logger.getLogger(ObjectPoolManager.class.getName());
    private static final ObjectPoolManager INSTANCE = new ObjectPoolManager();

    // 对象池映射
    private final ConcurrentHashMap<Class<?>, ObjectPool<?>> pools = new ConcurrentHashMap<>();
    
    // 全局统计
    private final PoolStats globalStats = new PoolStats();
    
    // 配置
    private final ObjectPoolConfig config = new ObjectPoolConfig();

    private ObjectPoolManager() {
        log.info("ObjectPoolManager initialized");
        startMonitoring();
        initializeCommonPools();
    }

    public static ObjectPoolManager getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化常用对象池
     */
    private void initializeCommonPools() {
        // StringBuilder池
        registerPool(StringBuilder.class, () -> new StringBuilder(256), 
                    sb -> sb.setLength(0), 100);
        
        // StringBuffer池
        registerPool(StringBuffer.class, () -> new StringBuffer(256), 
                    sb -> sb.setLength(0), 50);
        
        // byte[]池 - 不同大小
        registerPool(SmallByteArray.class, () -> new SmallByteArray(new byte[1024]), 
                    arr -> {}, 200);
        registerPool(MediumByteArray.class, () -> new MediumByteArray(new byte[4096]), 
                    arr -> {}, 100);
        registerPool(LargeByteArray.class, () -> new LargeByteArray(new byte[16384]), 
                    arr -> {}, 50);
        
        log.info("Common object pools initialized");
    }

    /**
     * 注册对象池
     */
    public <T> void registerPool(Class<T> type, Supplier<T> factory, 
                               ObjectResetter<T> resetter, int maxSize) {
        ObjectPool<T> pool = new ObjectPool<>(type, factory, resetter, maxSize);
        pools.put(type, pool);
        log.info("Registered object pool for type: " + type.getSimpleName() + 
                ", maxSize: " + maxSize);
    }

    /**
     * 从池中获取对象
     */
    @SuppressWarnings("unchecked")
    public <T> T borrowObject(Class<T> type) {
        ObjectPool<T> pool = (ObjectPool<T>) pools.get(type);
        if (pool == null) {
            globalStats.recordPoolMiss();
            log.warning("No pool registered for type: " + type.getSimpleName());
            return null;
        }
        
        T object = pool.borrow();
        if (object != null) {
            globalStats.recordPoolHit();
        } else {
            globalStats.recordPoolMiss();
        }
        
        return object;
    }

    /**
     * 归还对象到池中
     */
    @SuppressWarnings("unchecked")
    public <T> void returnObject(Class<T> type, T object) {
        if (object == null) {
            return;
        }
        
        ObjectPool<T> pool = (ObjectPool<T>) pools.get(type);
        if (pool == null) {
            log.warning("No pool registered for type: " + type.getSimpleName());
            return;
        }
        
        if (pool.returnObject(object)) {
            globalStats.recordReturn();
        } else {
            globalStats.recordReturnRejected();
        }
    }

    /**
     * 获取StringBuilder
     */
    public StringBuilder getStringBuilder() {
        StringBuilder sb = borrowObject(StringBuilder.class);
        return sb != null ? sb : new StringBuilder(256);
    }

    /**
     * 归还StringBuilder
     */
    public void returnStringBuilder(StringBuilder sb) {
        if (sb != null) {
            returnObject(StringBuilder.class, sb);
        }
    }

    /**
     * 获取小字节数组(1KB)
     */
    public byte[] getSmallByteArray() {
        SmallByteArray wrapper = borrowObject(SmallByteArray.class);
        return wrapper != null ? wrapper.array : new byte[1024];
    }

    /**
     * 归还小字节数组
     */
    public void returnSmallByteArray(byte[] array) {
        if (array != null && array.length == 1024) {
            returnObject(SmallByteArray.class, new SmallByteArray(array));
        }
    }

    /**
     * 获取中等字节数组(4KB)
     */
    public byte[] getMediumByteArray() {
        MediumByteArray wrapper = borrowObject(MediumByteArray.class);
        return wrapper != null ? wrapper.array : new byte[4096];
    }

    /**
     * 归还中等字节数组
     */
    public void returnMediumByteArray(byte[] array) {
        if (array != null && array.length == 4096) {
            returnObject(MediumByteArray.class, new MediumByteArray(array));
        }
    }

    /**
     * 获取大字节数组(16KB)
     */
    public byte[] getLargeByteArray() {
        LargeByteArray wrapper = borrowObject(LargeByteArray.class);
        return wrapper != null ? wrapper.array : new byte[16384];
    }

    /**
     * 归还大字节数组
     */
    public void returnLargeByteArray(byte[] array) {
        if (array != null && array.length == 16384) {
            returnObject(LargeByteArray.class, new LargeByteArray(array));
        }
    }

    /**
     * 获取池统计信息
     */
    public PoolStats getGlobalStats() {
        return globalStats;
    }

    /**
     * 获取特定类型的池统计
     */
    public PoolStats getPoolStats(Class<?> type) {
        ObjectPool<?> pool = pools.get(type);
        return pool != null ? pool.getStats() : new PoolStats();
    }

    /**
     * 清空所有池
     */
    public void clearAllPools() {
        pools.values().forEach(ObjectPool::clear);
        log.info("All object pools cleared");
    }

    /**
     * 启动监控
     */
    private void startMonitoring() {
        Thread monitoringThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(config.getMonitoringIntervalMs());
                    performMonitoring();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, "object-pool-monitor");
        
        monitoringThread.setDaemon(true);
        monitoringThread.start();
    }

    /**
     * 执行监控
     */
    private void performMonitoring() {
        try {
            if (log.isLoggable(Level.FINE)) {
                logPoolStats();
            }
            
            // 清理过期对象
            cleanupExpiredObjects();
            
            // 检查池健康状态
            checkPoolHealth();
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error during object pool monitoring", e);
        }
    }

    /**
     * 记录池统计信息
     */
    private void logPoolStats() {
        StringBuilder report = new StringBuilder("Object Pool Stats:\n");
        report.append("Global - Hits: ").append(globalStats.getHits())
              .append(", Misses: ").append(globalStats.getMisses())
              .append(", Hit Ratio: ").append(String.format("%.2f%%", globalStats.getHitRatio() * 100))
              .append("\n");
        
        pools.forEach((type, pool) -> {
            PoolStats stats = pool.getStats();
            report.append(type.getSimpleName())
                  .append(" - Size: ").append(pool.size())
                  .append("/").append(pool.getMaxSize())
                  .append(", Borrows: ").append(stats.getBorrows())
                  .append(", Returns: ").append(stats.getReturns())
                  .append("\n");
        });
        
        log.fine(report.toString());
    }

    /**
     * 清理过期对象
     */
    private void cleanupExpiredObjects() {
        pools.values().forEach(pool -> {
            if (pool.size() > pool.getMaxSize() / 2) {
                pool.cleanup();
            }
        });
    }

    /**
     * 检查池健康状态
     */
    private void checkPoolHealth() {
        pools.forEach((type, pool) -> {
            if (pool.getStats().getHitRatio() < 0.5 && pool.getStats().getBorrows() > 100) {
                log.warning("Low hit ratio for pool: " + type.getSimpleName() + 
                           ", hit ratio: " + String.format("%.2f%%", pool.getStats().getHitRatio() * 100));
            }
        });
    }

    /**
     * 对象池实现
     */
    private static class ObjectPool<T> {
        private final Class<T> type;
        private final Supplier<T> factory;
        private final ObjectResetter<T> resetter;
        private final int maxSize;
        private final ConcurrentLinkedQueue<T> pool = new ConcurrentLinkedQueue<>();
        private final AtomicInteger currentSize = new AtomicInteger(0);
        private final PoolStats stats = new PoolStats();

        public ObjectPool(Class<T> type, Supplier<T> factory, 
                         ObjectResetter<T> resetter, int maxSize) {
            this.type = type;
            this.factory = factory;
            this.resetter = resetter;
            this.maxSize = maxSize;
        }

        public T borrow() {
            T object = pool.poll();
            if (object != null) {
                currentSize.decrementAndGet();
                stats.recordBorrow();
                return object;
            }
            
            // 池中没有对象，创建新的
            stats.recordBorrow();
            return factory.get();
        }

        public boolean returnObject(T object) {
            if (object == null || currentSize.get() >= maxSize) {
                stats.recordReturnRejected();
                return false;
            }
            
            try {
                // 重置对象状态
                resetter.reset(object);
                
                // 放入池中
                pool.offer(object);
                currentSize.incrementAndGet();
                stats.recordReturn();
                return true;
            } catch (Exception e) {
                log.log(Level.WARNING, "Failed to reset object: " + type.getSimpleName(), e);
                stats.recordReturnRejected();
                return false;
            }
        }

        public void clear() {
            pool.clear();
            currentSize.set(0);
        }

        public void cleanup() {
            // 清理一半的对象
            int toRemove = currentSize.get() / 2;
            for (int i = 0; i < toRemove && !pool.isEmpty(); i++) {
                if (pool.poll() != null) {
                    currentSize.decrementAndGet();
                }
            }
        }

        public int size() {
            return currentSize.get();
        }

        public int getMaxSize() {
            return maxSize;
        }

        public PoolStats getStats() {
            return stats;
        }
    }

    /**
     * 对象重置接口
     */
    @FunctionalInterface
    public interface ObjectResetter<T> {
        void reset(T object) throws Exception;
    }

    /**
     * 池统计信息
     */
    public static class PoolStats {
        private final LongAdder hits = new LongAdder();
        private final LongAdder misses = new LongAdder();
        private final LongAdder borrows = new LongAdder();
        private final LongAdder returns = new LongAdder();
        private final LongAdder returnRejected = new LongAdder();

        public void recordPoolHit() {
            hits.increment();
        }

        public void recordPoolMiss() {
            misses.increment();
        }

        public void recordBorrow() {
            borrows.increment();
        }

        public void recordReturn() {
            returns.increment();
        }

        public void recordReturnRejected() {
            returnRejected.increment();
        }

        public long getHits() {
            return hits.sum();
        }

        public long getMisses() {
            return misses.sum();
        }

        public long getBorrows() {
            return borrows.sum();
        }

        public long getReturns() {
            return returns.sum();
        }

        public long getReturnRejected() {
            return returnRejected.sum();
        }

        public double getHitRatio() {
            long totalRequests = hits.sum() + misses.sum();
            return totalRequests > 0 ? (double) hits.sum() / totalRequests : 0.0;
        }
    }

    /**
     * 对象池配置
     */
    public static class ObjectPoolConfig {
        private long monitoringIntervalMs = 60000; // 1分钟监控一次

        public long getMonitoringIntervalMs() {
            return monitoringIntervalMs;
        }

        public void setMonitoringIntervalMs(long monitoringIntervalMs) {
            this.monitoringIntervalMs = monitoringIntervalMs;
        }
    }

    // 字节数组包装类
    public static class SmallByteArray {
        public final byte[] array;
        public SmallByteArray(byte[] array) {
            this.array = array;
        }
    }

    public static class MediumByteArray {
        public final byte[] array;
        public MediumByteArray(byte[] array) {
            this.array = array;
        }
    }

    public static class LargeByteArray {
        public final byte[] array;
        public LargeByteArray(byte[] array) {
            this.array = array;
        }
    }
}