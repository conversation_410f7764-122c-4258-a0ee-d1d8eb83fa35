/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async;

import com.yeepay.g3.yop.ext.gateway.backend.AsyncApiBackend;
import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.gateway.async.AsyncFilterChainMetrics;
import com.yeepay.g3.yop.gateway.backend.async.exception.AsyncBackendException;

import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 异步API后端抽象基类<br/>
 * description: 提供异步后端调用的通用基础设施，包括超时控制、重试机制、熔断保护<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public abstract class AbstractAsyncApiBackend implements AsyncApiBackend, AutoCloseable {

    private static final Logger log = Logger.getLogger(AbstractAsyncApiBackend.class.getName());

    // 常量定义
    private static final int DEFAULT_SCHEDULER_POOL_SIZE = 4;
    private static final long MAX_RETRY_DELAY_MS = 10000L; // 最大重试延迟10秒
    private static final long INITIAL_RETRY_DELAY_MS = 1000L; // 初始重试延迟1秒

    // 业务线程池，用于CPU密集型操作
    protected final ExecutorService businessExecutor;

    // I/O线程池，用于异步I/O操作
    protected final ExecutorService ioExecutor;

    // 调度器，用于超时和重试
    protected final ScheduledExecutorService scheduler;

    // 熔断器映射
    private final ConcurrentHashMap<String, CircuitBreakerState> circuitBreakers =
        new ConcurrentHashMap<>();

    // 性能监控
    protected final AsyncFilterChainMetrics metrics = AsyncFilterChainMetrics.getInstance();

    // 资源关闭标志
    private volatile boolean closed = false;

    /**
     * 默认构造函数，使用默认线程池配置
     */
    protected AbstractAsyncApiBackend() {
        this(createDefaultBusinessExecutor(), createDefaultIOExecutor(), createDefaultScheduler());
    }

    /**
     * 自定义线程池构造函数
     */
    protected AbstractAsyncApiBackend(ExecutorService businessExecutor,
                                    ExecutorService ioExecutor,
                                    ScheduledExecutorService scheduler) {
        this.businessExecutor = businessExecutor;
        this.ioExecutor = ioExecutor;
        this.scheduler = scheduler;
    }

    /**
     * 创建默认业务线程池
     */
    private static ExecutorService createDefaultBusinessExecutor() {
        return ForkJoinPool.commonPool();
    }

    /**
     * 创建默认I/O线程池
     */
    private static ExecutorService createDefaultIOExecutor() {
        return Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "async-backend-io-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * 创建默认调度器
     */
    private static ScheduledExecutorService createDefaultScheduler() {
        return Executors.newScheduledThreadPool(DEFAULT_SCHEDULER_POOL_SIZE, r -> {
            Thread t = new Thread(r, "async-backend-scheduler-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        });
    }

    @Override
    public final CompletableFuture<Object> invokeAsync(AsyncServerWebExchange exchange) {
        if (closed) {
            CompletableFuture<Object> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(
                new AsyncBackendException(getName(), AsyncBackendException.ErrorCode.CONFIG_ERROR,
                    "Backend is closed: " + getName()));
            return failedFuture;
        }

        String backendName = getName();
        long startTime = System.currentTimeMillis();

        // 检查熔断器状态
        if (supportsCircuitBreaker() && isCircuitBreakerOpen(backendName)) {
            CompletableFuture<Object> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(
                new AsyncBackendException(backendName, AsyncBackendException.ErrorCode.CIRCUIT_BREAKER_OPEN,
                    "Circuit breaker is open for backend: " + backendName));
            return failedFuture;
        }

        // 记录开始执行
        metrics.recordFilterStart("backend." + backendName);

        CompletableFuture<Object> future = doInvokeAsync(exchange);

        // 添加超时控制
        if (getTimeoutMillis() > 0) {
            future = addTimeout(future, getTimeoutMillis());
        }

        // 添加重试机制
        if (supportsRetry() && getRetryCount() > 0) {
            future = addRetry(future, exchange, getRetryCount());
        }

        // 添加熔断器逻辑
        if (supportsCircuitBreaker()) {
            future = addCircuitBreaker(future, backendName);
        }

        // 添加性能监控
        future = future.whenComplete((result, throwable) -> {
            long executionTime = System.currentTimeMillis() - startTime;
            metrics.recordFilterComplete("backend." + backendName, executionTime);

            if (throwable != null) {
                metrics.recordFilterError("backend." + backendName);
                // 根据异常类型选择合适的日志级别
                Level logLevel = (throwable instanceof AsyncBackendException) ? Level.WARNING : Level.SEVERE;
                log.log(logLevel, "Backend " + backendName + " execution failed in " + executionTime + "ms", throwable);
            } else {
                log.fine("Backend " + backendName + " execution completed in " + executionTime + "ms");
            }
        });

        return future;
    }

    /**
     * 子类实现具体的异步调用逻辑
     *
     * @param exchange 异步服务网络交换器
     * @return CompletableFuture<Object> 异步调用结果
     */
    protected abstract CompletableFuture<Object> doInvokeAsync(AsyncServerWebExchange exchange);

    /**
     * 添加超时控制
     */
    protected CompletableFuture<Object> addTimeout(CompletableFuture<Object> future, long timeoutMillis) {
        CompletableFuture<Object> timeoutFuture = new CompletableFuture<>();

        // 设置超时
        ScheduledFuture<?> timeoutTask = scheduler.schedule(() -> {
            if (!timeoutFuture.isDone()) {
                timeoutFuture.completeExceptionally(
                    new AsyncBackendException(getName(), AsyncBackendException.ErrorCode.TIMEOUT,
                        "Backend call timeout after " + timeoutMillis + "ms"));
            }
        }, timeoutMillis, TimeUnit.MILLISECONDS);

        // 原始调用完成时
        future.whenComplete((result, throwable) -> {
            timeoutTask.cancel(false);
            if (throwable != null) {
                timeoutFuture.completeExceptionally(throwable);
            } else {
                timeoutFuture.complete(result);
            }
        });

        return timeoutFuture;
    }

    /**
     * 添加重试机制
     */
    protected CompletableFuture<Object> addRetry(CompletableFuture<Object> future,
                                                AsyncServerWebExchange exchange,
                                                int maxRetries) {
        return future.handle((result, throwable) -> {
            if (throwable != null && maxRetries > 0) {
                return retryWithBackoff(exchange, maxRetries, 1, throwable);
            }
            if (throwable != null) {
                CompletableFuture<Object> failedFuture = new CompletableFuture<>();
                failedFuture.completeExceptionally(throwable);
                return failedFuture;
            }
            return CompletableFuture.completedFuture(result);
        }).thenCompose(Function.identity());
    }

    /**
     * 指数退避重试
     */
    private CompletableFuture<Object> retryWithBackoff(AsyncServerWebExchange exchange,
                                                      int remainingRetries,
                                                      int attempt,
                                                      Throwable originalException) {
        if (remainingRetries <= 0) {
            CompletableFuture<Object> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(
                new AsyncBackendException(getName(), AsyncBackendException.ErrorCode.MAX_RETRY_EXCEEDED,
                    "Maximum retry attempts exceeded", originalException));
            return failedFuture;
        }

        long delayMs = Math.min(INITIAL_RETRY_DELAY_MS * (1L << (attempt - 1)), MAX_RETRY_DELAY_MS);

        return delayedFuture(delayMs, TimeUnit.MILLISECONDS)
            .thenCompose(ignored -> doInvokeAsync(exchange))
            .handle((result, throwable) -> {
                if (throwable != null) {
                    log.warning("Retry attempt " + attempt + " failed for backend " + getName() + ": " + throwable.getMessage());
                    return retryWithBackoff(exchange, remainingRetries - 1, attempt + 1, originalException);
                }
                return CompletableFuture.completedFuture(result);
            })
            .thenCompose(Function.identity());
    }

    /**
     * 添加熔断器
     */
    protected CompletableFuture<Object> addCircuitBreaker(CompletableFuture<Object> future, String backendName) {
        return future.whenComplete((result, throwable) -> {
            CircuitBreakerState state = circuitBreakers.computeIfAbsent(
                backendName, name -> new CircuitBreakerState());

            if (throwable != null) {
                state.recordFailure();
            } else {
                state.recordSuccess();
            }
        });
    }

    /**
     * 检查熔断器是否打开
     */
    protected boolean isCircuitBreakerOpen(String backendName) {
        CircuitBreakerState state = circuitBreakers.get(backendName);
        return state != null && state.isOpen();
    }

    /**
     * 熔断器状态
     */
    private static class CircuitBreakerState {
        private static final int FAILURE_THRESHOLD = 5;
        private static final long RECOVERY_TIMEOUT = 60000; // 1分钟

        private volatile int consecutiveFailures = 0;
        private volatile long lastFailureTime = 0;
        private volatile boolean open = false;

        synchronized void recordSuccess() {
            consecutiveFailures = 0;
            open = false;
        }

        synchronized void recordFailure() {
            consecutiveFailures++;
            lastFailureTime = System.currentTimeMillis();
            
            if (consecutiveFailures >= FAILURE_THRESHOLD) {
                open = true;
            }
        }

        boolean isOpen() {
            if (!open) {
                return false;
            }
            
            // 检查是否可以尝试恢复
            if (System.currentTimeMillis() - lastFailureTime > RECOVERY_TIMEOUT) {
                synchronized (this) {
                    if (System.currentTimeMillis() - lastFailureTime > RECOVERY_TIMEOUT) {
                        open = false;
                        consecutiveFailures = 0;
                    }
                }
            }
            
            return open;
        }
    }

    /**
     * 创建延迟的CompletableFuture
     */
    protected CompletableFuture<Void> delayedFuture(long delay, TimeUnit unit) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        scheduler.schedule(() -> future.complete(null), delay, unit);
        return future;
    }

    /**
     * 在业务线程池中执行CPU密集型操作
     */
    protected <T> CompletableFuture<T> executeInBusinessPool(Supplier<T> supplier) {
        if (closed) {
            CompletableFuture<T> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(
                new AsyncBackendException(getName(), AsyncBackendException.ErrorCode.CONFIG_ERROR,
                    "Backend is closed"));
            return failedFuture;
        }
        return CompletableFuture.supplyAsync(supplier, businessExecutor);
    }

    /**
     * 在I/O线程池中执行I/O操作
     */
    protected <T> CompletableFuture<T> executeInIOPool(Supplier<T> supplier) {
        if (closed) {
            CompletableFuture<T> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(
                new AsyncBackendException(getName(), AsyncBackendException.ErrorCode.CONFIG_ERROR,
                    "Backend is closed"));
            return failedFuture;
        }
        return CompletableFuture.supplyAsync(supplier, ioExecutor);
    }

    @Override
    public CompletableFuture<Boolean> healthCheck() {
        if (closed) {
            return CompletableFuture.completedFuture(false);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                return doHealthCheck();
            } catch (Exception e) {
                log.log(Level.WARNING, "Health check failed for backend " + getName(), e);
                return false;
            }
        }, ioExecutor);
    }

    /**
     * 子类可重写的健康检查实现
     */
    protected boolean doHealthCheck() {
        return true;
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() throws Exception {
        if (closed) {
            return;
        }

        log.info("Closing AsyncApiBackend: " + getName());
        closed = true;

        // 关闭线程池
        shutdownExecutor(businessExecutor, "business");
        shutdownExecutor(ioExecutor, "io");
        shutdownExecutor(scheduler, "scheduler");

        // 清理熔断器
        circuitBreakers.clear();

        log.info("AsyncApiBackend closed: " + getName());
    }

    /**
     * 优雅关闭线程池
     */
    private void shutdownExecutor(ExecutorService executor, String name) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            log.fine("Shutting down " + name + " executor");
            executor.shutdown();

            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warning(name + " executor did not terminate gracefully, forcing shutdown");
                executor.shutdownNow();

                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.severe(name + " executor did not terminate");
                }
            }
        } catch (InterruptedException e) {
            log.warning("Interrupted while shutting down " + name + " executor");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 检查是否已关闭
     */
    public boolean isClosed() {
        return closed;
    }
}