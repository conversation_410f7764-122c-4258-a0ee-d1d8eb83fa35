/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter;

import com.yeepay.g3.yop.ext.gateway.filter.AsyncGatewayFilter;
import com.yeepay.g3.yop.ext.gateway.filter.AsyncGatewayFilterChain;
import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.service.SystemServiceUnavailableException;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.g3.yop.ext.gateway.context.impl.InvokeProcessContext;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.yeepay.g3.yop.gateway.async.AsyncFilterChainMetrics;
import com.yeepay.g3.yop.gateway.async.AsyncFilterChainExecutorManager;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * title: 异步网关过滤器处理链的默认实现<br/>
 * description: 支持串行、并行混合执行的高性能异步过滤器链<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
@Slf4j
public class DefaultAsyncGatewayFilterChain implements AsyncGatewayFilterChain {

    @Getter
    private final List<AsyncGatewayFilter> filters;

    private final int currentIndex;
    private final ExecutorService businessExecutor;
    private final ScheduledExecutorService timeoutExecutor;

    // 使用单例监控器，避免线程安全问题
    private static final AsyncFilterChainMetrics METRICS = AsyncFilterChainMetrics.getInstance();

    // 使用线程池管理器，避免资源泄漏
    private static final AsyncFilterChainExecutorManager EXECUTOR_MANAGER = AsyncFilterChainExecutorManager.getInstance();

    public DefaultAsyncGatewayFilterChain(List<AsyncGatewayFilter> filters) {
        this(filters, 0, EXECUTOR_MANAGER.getBusinessExecutor(), EXECUTOR_MANAGER.getTimeoutExecutor());
    }

    private DefaultAsyncGatewayFilterChain(List<AsyncGatewayFilter> filters, int currentIndex, 
                                         ExecutorService businessExecutor, ScheduledExecutorService timeoutExecutor) {
        this.filters = filters;
        this.currentIndex = currentIndex;
        this.businessExecutor = businessExecutor;
        this.timeoutExecutor = timeoutExecutor;
    }

    @Override
    public CompletableFuture<Void> filter(AsyncServerWebExchange exchange) {
        exchange.markAsyncStarted();
        
        if (currentIndex >= filters.size()) {
            logDebug("Filter chain completed for request: " + exchange.getRequestId());
            return CompletableFuture.completedFuture(null);
        }

        // 按并行组分组执行
        Map<String, List<AsyncGatewayFilter>> parallelGroups = groupFiltersForParallelExecution();
        
        if (parallelGroups.size() == 1 && parallelGroups.containsKey(null)) {
            // 串行执行单个过滤器
            return executeFilterSerially(exchange, filters.get(currentIndex));
        } else {
            // 并行执行同组过滤器
            return executeFiltersInParallel(exchange, parallelGroups);
        }
    }

    /**
     * 将过滤器按并行组分组
     */
    private Map<String, List<AsyncGatewayFilter>> groupFiltersForParallelExecution() {
        Map<String, List<AsyncGatewayFilter>> groups = new LinkedHashMap<>();
        
        int index = currentIndex;
        while (index < filters.size()) {
            AsyncGatewayFilter filter = filters.get(index);
            String groupName = filter.supportsParallelExecution() ? filter.getParallelGroup() : null;
            
            groups.computeIfAbsent(groupName, k -> new ArrayList<>()).add(filter);
            index++;
            
            // 如果遇到不支持并行的过滤器，只处理当前组
            if (!filter.supportsParallelExecution()) {
                break;
            }
            
            // 如果下一个过滤器属于不同的组，停止分组
            if (index < filters.size()) {
                AsyncGatewayFilter nextFilter = filters.get(index);
                String nextGroupName = nextFilter.supportsParallelExecution() ? nextFilter.getParallelGroup() : null;
                if (!Objects.equals(groupName, nextGroupName)) {
                    break;
                }
            }
        }
        
        return groups;
    }

    /**
     * 串行执行单个过滤器
     */
    private CompletableFuture<Void> executeFilterSerially(AsyncServerWebExchange exchange, AsyncGatewayFilter filter) {
        logDebug("Executing filter serially: " + filter.getName() + " for request: " + exchange.getRequestId());
        
        return executeFilterWithMonitoring(exchange, filter)
                .thenCompose(v -> {
                    // 创建下一个过滤器链
                    AsyncGatewayFilterChain nextChain = createNextChain();
                    return nextChain.filter(exchange);
                })
                .exceptionally(throwable -> {
                    handleFilterExceptionSync(exchange, filter, throwable);
                    return null;
                });
    }

    /**
     * 并行执行同组过滤器
     */
    private CompletableFuture<Void> executeFiltersInParallel(AsyncServerWebExchange exchange, 
                                                           Map<String, List<AsyncGatewayFilter>> parallelGroups) {
        log.debug("Executing filters in parallel groups: {} for request: {}", 
                 parallelGroups.keySet(), exchange.getRequestId());

        List<CompletableFuture<Void>> parallelTasks = new ArrayList<>();
        
        for (Map.Entry<String, List<AsyncGatewayFilter>> entry : parallelGroups.entrySet()) {
            String groupName = entry.getKey();
            List<AsyncGatewayFilter> groupFilters = entry.getValue();
            
            if (groupFilters.size() == 1) {
                // 单个过滤器
                AsyncGatewayFilter filter = groupFilters.get(0);
                CompletableFuture<Void> task = executeFilterWithMonitoring(exchange, filter);
                parallelTasks.add(task);
            } else {
                // 同组多个过滤器并行执行
                CompletableFuture<Void> groupTask = executeGroupFiltersInParallel(exchange, groupName, groupFilters);
                parallelTasks.add(groupTask);
            }
        }

        // 等待所有并行任务完成
        return CompletableFuture.allOf(parallelTasks.toArray(new CompletableFuture[0]))
                .thenCompose(v -> {
                    // 计算下一个索引
                    int nextIndex = currentIndex + parallelGroups.values().stream()
                            .mapToInt(List::size).sum();
                    
                    if (nextIndex < filters.size()) {
                        AsyncGatewayFilterChain nextChain = new DefaultAsyncGatewayFilterChain(
                                filters, nextIndex, businessExecutor, timeoutExecutor);
                        return nextChain.filter(exchange);
                    } else {
                        return CompletableFuture.completedFuture(null);
                    }
                });
    }

    /**
     * 执行同组过滤器的并行任务
     */
    private CompletableFuture<Void> executeGroupFiltersInParallel(AsyncServerWebExchange exchange, 
                                                                String groupName, 
                                                                List<AsyncGatewayFilter> groupFilters) {
        log.debug("Executing group filters in parallel: group={}, filters={} for request: {}", 
                 groupName, groupFilters.stream().map(AsyncGatewayFilter::getName).collect(Collectors.toList()), 
                 exchange.getRequestId());

        List<CompletableFuture<Void>> tasks = groupFilters.stream()
                .map(filter -> {
                    // 为每个并行过滤器创建子交换器
                    AsyncServerWebExchange childExchange = exchange.createChildExchange(filter.getName());
                    return executeFilterWithMonitoring(childExchange, filter);
                })
                .collect(Collectors.toList());

        return CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0]));
    }

    /**
     * 执行过滤器并添加监控
     */
    private CompletableFuture<Void> executeFilterWithMonitoring(AsyncServerWebExchange exchange, AsyncGatewayFilter filter) {
        if (isClosed(filter)) {
            log.info("Filter {} is closed for request: {}", filter.getName(), exchange.getRequestId());
            return CompletableFuture.completedFuture(null);
        }

        long startTime = System.currentTimeMillis();
        
        return filter.isEnabled(exchange)
                .thenCompose(enabled -> {
                    if (!enabled) {
                        log.debug("Filter {} is disabled for request: {}", filter.getName(), exchange.getRequestId());
                        return CompletableFuture.completedFuture(null);
                    }
                    
                    // 根据过滤器类型选择执行器
                    CompletableFuture<Void> filterTask;
                    if (filter.isBlocking()) {
                        // 阻塞操作在业务线程池中执行
                        filterTask = CompletableFuture.runAsync(() -> {
                            try {
                                filter.filter(exchange, this).join();
                            } catch (Exception e) {
                                throw new CompletionException(e);
                            }
                        }, businessExecutor);
                    } else {
                        // 非阻塞操作直接执行
                        filterTask = filter.filter(exchange, this);
                    }
                    
                    // 添加超时控制
                    return addTimeoutControl(filterTask, filter.getTimeoutMillis(), filter.getName());
                })
                .whenComplete((result, throwable) -> {
                    // 记录性能指标
                    long executionTime = System.currentTimeMillis() - startTime;
                    recordFilterMetrics(filter.getName(), executionTime);
                    
                    if (throwable != null) {
                        log.error("Filter {} execution failed for request: {}, time: {}ms", 
                                filter.getName(), exchange.getRequestId(), executionTime, throwable);
                    } else {
                        log.debug("Filter {} executed successfully for request: {}, time: {}ms", 
                                filter.getName(), exchange.getRequestId(), executionTime);
                    }
                });
    }

    /**
     * 添加超时控制
     */
    private CompletableFuture<Void> addTimeoutControl(CompletableFuture<Void> future, long timeoutMillis, String filterName) {
        CompletableFuture<Void> timeoutFuture = new CompletableFuture<>();
        
        // 设置超时
        ScheduledFuture<?> timeoutTask = timeoutExecutor.schedule(() -> {
            if (!future.isDone()) {
                String errorMsg = String.format("Filter %s execution timeout after %dms", filterName, timeoutMillis);
                timeoutFuture.completeExceptionally(new TimeoutException(errorMsg));
            }
        }, timeoutMillis, TimeUnit.MILLISECONDS);
        
        // 正常完成或异常时取消超时任务
        future.whenComplete((result, throwable) -> {
            timeoutTask.cancel(false);
            if (throwable != null) {
                timeoutFuture.completeExceptionally(throwable);
            } else {
                timeoutFuture.complete(result);
            }
        });
        
        return timeoutFuture;
    }

    /**
     * 处理过滤器异常 - 同步版本
     */
    private void handleFilterExceptionSync(AsyncServerWebExchange exchange, AsyncGatewayFilter filter, Throwable throwable) {
        logError("Filter " + filter.getName() + " execution failed for request: " + exchange.getRequestId(), throwable);

        try {
            if (throwable instanceof CompletionException && throwable.getCause() instanceof InternalException) {
                InternalException internalException = (InternalException) throwable.getCause();
                setExchangeErrorAsync(exchange, internalException, new Response(internalException));
            } else if (throwable instanceof InternalException) {
                InternalException internalException = (InternalException) throwable;
                setExchangeErrorAsync(exchange, internalException, new Response(internalException));
            } else {
                if (filter.supportBackup()) {
                    logWarning("Filter " + filter.getName() + " failed but supports backup, continuing chain for request: " + exchange.getRequestId());
                    return;
                }

                InternalException internalException = new SystemServiceUnavailableException();
                Response response = new Response(internalException);
                setExchangeErrorAsync(exchange, internalException, response);
            }
        } catch (Exception e) {
            logError("Error handling filter exception for request: " + exchange.getRequestId(), e);
        }
    }

    /**
     * 处理过滤器异常 - 异步版本
     */
    private CompletableFuture<Void> handleFilterException(AsyncServerWebExchange exchange, AsyncGatewayFilter filter, Throwable throwable) {
        return CompletableFuture.runAsync(() -> {
            handleFilterExceptionSync(exchange, filter, throwable);
        });
    }

    /**
     * 设置交换器错误状态 - 异步版本
     */
    private CompletableFuture<Void> setExchangeErrorAsync(AsyncServerWebExchange exchange, InternalException internalException, Response response) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 设置错误信息到异步上下文
                exchange.setAsyncContext("internal_exception", internalException);
                exchange.setAsyncContext("error_response", response);
                exchange.setAsyncContext("error_timestamp", System.currentTimeMillis());

                // 记录错误到监控系统
                METRICS.recordFilterError("error_handler");

                // 如果可能，也设置到同步属性中
                if (exchange.getAttributes() != null) {
                    exchange.getAttributes().put("internal_exception", internalException);
                    exchange.getAttributes().put("error_response", response);
                }
            } catch (Exception e) {
                logError("Failed to set exchange error for request: " + exchange.getRequestId(), e);
            }
        });
    }

    /**
     * 日志辅助方法
     */
    private void logError(String message, Throwable throwable) {
        log.severe(message + " - " + throwable.getMessage());
    }

    private void logWarning(String message) {
        log.warning(message);
    }

    private void logInfo(String message) {
        log.info(message);
    }

    private void logDebug(String message) {
        log.fine(message);
    }

    /**
     * 记录过滤器性能指标
     */
    private void recordFilterMetrics(String filterName, long executionTime) {
        METRICS.recordFilterStart(filterName);
        METRICS.recordFilterComplete(filterName, executionTime);
    }

    /**
     * 检查过滤器是否被关闭
     */
    private boolean isClosed(AsyncGatewayFilter filter) {
        Map<String, Boolean> filterSwitchMap = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_FILTER_SWITCH, Map.class);
        if (MapUtils.isEmpty(filterSwitchMap)) {
            return false;
        }
        Boolean close = filterSwitchMap.get(filter.getName());
        return Boolean.FALSE.equals(close);
    }

    /**
     * 创建下一个过滤器链
     */
    private AsyncGatewayFilterChain createNextChain() {
        return new DefaultAsyncGatewayFilterChain(filters, currentIndex + 1, businessExecutor, timeoutExecutor);
    }

    @Override
    public int getRemainingFiltersCount() {
        return Math.max(0, filters.size() - currentIndex);
    }

    @Override
    public String getCurrentFilterName() {
        if (currentIndex < filters.size()) {
            return filters.get(currentIndex).getName();
        }
        return "COMPLETED";
    }

    @Override
    public boolean hasMoreFilters() {
        return currentIndex < filters.size();
    }

    // 移除静态线程池创建方法，使用线程池管理器

    /**
     * 获取性能统计信息
     */
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        AsyncFilterChainMetrics.SystemStats systemStats = METRICS.getSystemStats();
        metrics.put("systemStats", systemStats);
        metrics.put("activeRequests", systemStats.getActiveRequests());
        metrics.put("parallelExecutionRatio", systemStats.getParallelExecutionRatio());
        return metrics;
    }

    /**
     * 获取过滤器列表 - 替代@Getter
     */
    public List<AsyncGatewayFilter> getFilters() {
        return filters;
    }
}