/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 零拷贝数据传输优化器<br/>
 * description: 实现高性能零拷贝数据传输机制，减少内存拷贝操作<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class ZeroCopyDataTransfer {

    private static final Logger log = Logger.getLogger(ZeroCopyDataTransfer.class.getName());
    private static final ZeroCopyDataTransfer INSTANCE = new ZeroCopyDataTransfer();

    // 性能统计
    private final TransferStats stats = new TransferStats();
    
    // 内存池管理器
    private final OffHeapMemoryPoolManager memoryPool = OffHeapMemoryPoolManager.getInstance();

    private ZeroCopyDataTransfer() {
        log.info("ZeroCopyDataTransfer initialized");
    }

    public static ZeroCopyDataTransfer getInstance() {
        return INSTANCE;
    }

    /**
     * 零拷贝组合多个ByteBuffer
     */
    public CompositeByteBuffer composeBuffers(ByteBuffer... buffers) {
        stats.recordComposition(buffers.length);
        return new CompositeByteBuffer(buffers);
    }

    /**
     * 零拷贝组合多个ByteBuffer
     */
    public CompositeByteBuffer composeBuffers(List<ByteBuffer> buffers) {
        stats.recordComposition(buffers.size());
        return new CompositeByteBuffer(buffers.toArray(new ByteBuffer[0]));
    }

    /**
     * 零拷贝文件传输（使用sendfile系统调用）
     */
    public long transferFile(FileChannel source, WritableByteChannel target, 
                           long position, long count) throws IOException {
        long startTime = System.nanoTime();
        try {
            long transferred = source.transferTo(position, count, target);
            stats.recordFileTransfer(transferred, System.nanoTime() - startTime);
            return transferred;
        } catch (IOException e) {
            stats.recordTransferError();
            throw e;
        }
    }

    /**
     * 零拷贝文件读取到内存
     */
    public long transferFromFile(ReadableByteChannel source, FileChannel target, 
                               long position, long count) throws IOException {
        long startTime = System.nanoTime();
        try {
            long transferred = target.transferFrom(source, position, count);
            stats.recordFileTransfer(transferred, System.nanoTime() - startTime);
            return transferred;
        } catch (IOException e) {
            stats.recordTransferError();
            throw e;
        }
    }

    /**
     * 内存映射文件读取
     */
    public ByteBuffer mapFile(Path filePath, long position, long size) throws IOException {
        long startTime = System.nanoTime();
        try (FileChannel channel = FileChannel.open(filePath, StandardOpenOption.READ)) {
            ByteBuffer mapped = channel.map(FileChannel.MapMode.READ_ONLY, position, size);
            stats.recordMemoryMapping(size, System.nanoTime() - startTime);
            return mapped;
        } catch (IOException e) {
            stats.recordTransferError();
            throw e;
        }
    }

    /**
     * 内存映射文件写入
     */
    public ByteBuffer mapFileForWrite(Path filePath, long position, long size) throws IOException {
        long startTime = System.nanoTime();
        try (FileChannel channel = FileChannel.open(filePath, 
                StandardOpenOption.READ, StandardOpenOption.WRITE, StandardOpenOption.CREATE)) {
            ByteBuffer mapped = channel.map(FileChannel.MapMode.READ_WRITE, position, size);
            stats.recordMemoryMapping(size, System.nanoTime() - startTime);
            return mapped;
        } catch (IOException e) {
            stats.recordTransferError();
            throw e;
        }
    }

    /**
     * 零拷贝字节数组合并
     */
    public ByteBuffer mergeBuffers(ByteBuffer... buffers) {
        int totalSize = 0;
        for (ByteBuffer buffer : buffers) {
            totalSize += buffer.remaining();
        }
        
        ByteBuffer merged = memoryPool.allocateDirectBuffer(totalSize);
        for (ByteBuffer buffer : buffers) {
            merged.put(buffer);
        }
        merged.flip();
        
        stats.recordBufferMerge(buffers.length, totalSize);
        return merged;
    }

    /**
     * 零拷贝字节数组分割
     */
    public List<ByteBuffer> splitBuffer(ByteBuffer source, int... chunkSizes) {
        List<ByteBuffer> chunks = new ArrayList<>();
        int originalPosition = source.position();
        
        for (int size : chunkSizes) {
            if (source.remaining() < size) {
                size = source.remaining();
            }
            
            int endPosition = source.position() + size;
            source.limit(endPosition);
            ByteBuffer chunk = source.slice();
            chunks.add(chunk);
            
            source.position(endPosition);
            source.limit(source.capacity());
        }
        
        // 重置原buffer位置
        source.position(originalPosition);
        
        stats.recordBufferSplit(chunkSizes.length);
        return chunks;
    }

    /**
     * 获取传输统计
     */
    public TransferStats getStats() {
        return stats;
    }

    /**
     * 复合字节缓冲区实现
     */
    public static class CompositeByteBuffer {
        private final ByteBuffer[] buffers;
        private final int totalSize;
        private int currentBufferIndex = 0;
        private int globalPosition = 0;

        public CompositeByteBuffer(ByteBuffer[] buffers) {
            this.buffers = buffers.clone();
            int size = 0;
            for (ByteBuffer buffer : buffers) {
                size += buffer.remaining();
            }
            this.totalSize = size;
        }

        /**
         * 获取总大小
         */
        public int totalSize() {
            return totalSize;
        }

        /**
         * 获取当前位置
         */
        public int position() {
            return globalPosition;
        }

        /**
         * 剩余字节数
         */
        public int remaining() {
            return totalSize - globalPosition;
        }

        /**
         * 是否还有剩余数据
         */
        public boolean hasRemaining() {
            return remaining() > 0;
        }

        /**
         * 读取单个字节
         */
        public byte get() {
            if (!hasRemaining()) {
                throw new java.nio.BufferUnderflowException();
            }

            // 找到当前有效的buffer
            while (currentBufferIndex < buffers.length && 
                   !buffers[currentBufferIndex].hasRemaining()) {
                currentBufferIndex++;
            }

            if (currentBufferIndex >= buffers.length) {
                throw new java.nio.BufferUnderflowException();
            }

            globalPosition++;
            return buffers[currentBufferIndex].get();
        }

        /**
         * 读取字节数组
         */
        public int get(byte[] dst, int offset, int length) {
            if (length == 0) {
                return 0;
            }

            int totalRead = 0;
            int remainingToRead = Math.min(length, remaining());

            while (remainingToRead > 0 && currentBufferIndex < buffers.length) {
                // 跳过空的buffer
                while (currentBufferIndex < buffers.length && 
                       !buffers[currentBufferIndex].hasRemaining()) {
                    currentBufferIndex++;
                }

                if (currentBufferIndex >= buffers.length) {
                    break;
                }

                ByteBuffer currentBuffer = buffers[currentBufferIndex];
                int toRead = Math.min(remainingToRead, currentBuffer.remaining());
                
                currentBuffer.get(dst, offset + totalRead, toRead);
                totalRead += toRead;
                remainingToRead -= toRead;
                globalPosition += toRead;
            }

            return totalRead > 0 ? totalRead : -1;
        }

        /**
         * 读取字节数组
         */
        public int get(byte[] dst) {
            return get(dst, 0, dst.length);
        }

        /**
         * 转换为字节数组（会产生内存拷贝）
         */
        public byte[] toByteArray() {
            byte[] result = new byte[totalSize];
            int position = 0;

            for (ByteBuffer buffer : buffers) {
                int remaining = buffer.remaining();
                buffer.get(result, position, remaining);
                position += remaining;
            }

            return result;
        }

        /**
         * 创建视图ByteBuffer数组
         */
        public ByteBuffer[] getBuffers() {
            return buffers.clone();
        }

        /**
         * 重置所有buffer位置
         */
        public void rewind() {
            for (ByteBuffer buffer : buffers) {
                buffer.rewind();
            }
            currentBufferIndex = 0;
            globalPosition = 0;
        }
    }

    /**
     * 传输统计信息
     */
    public static class TransferStats {
        private final LongAdder compositionCount = new LongAdder();
        private final LongAdder fileTransferCount = new LongAdder();
        private final LongAdder fileTransferBytes = new LongAdder();
        private final LongAdder fileTransferTime = new LongAdder();
        private final LongAdder memoryMappingCount = new LongAdder();
        private final LongAdder memoryMappingBytes = new LongAdder();
        private final LongAdder memoryMappingTime = new LongAdder();
        private final LongAdder bufferMergeCount = new LongAdder();
        private final LongAdder bufferMergeBytes = new LongAdder();
        private final LongAdder bufferSplitCount = new LongAdder();
        private final LongAdder transferErrorCount = new LongAdder();

        public void recordComposition(int bufferCount) {
            compositionCount.increment();
        }

        public void recordFileTransfer(long bytes, long timeNanos) {
            fileTransferCount.increment();
            fileTransferBytes.add(bytes);
            fileTransferTime.add(timeNanos);
        }

        public void recordMemoryMapping(long bytes, long timeNanos) {
            memoryMappingCount.increment();
            memoryMappingBytes.add(bytes);
            memoryMappingTime.add(timeNanos);
        }

        public void recordBufferMerge(int bufferCount, int totalBytes) {
            bufferMergeCount.increment();
            bufferMergeBytes.add(totalBytes);
        }

        public void recordBufferSplit(int chunkCount) {
            bufferSplitCount.increment();
        }

        public void recordTransferError() {
            transferErrorCount.increment();
        }

        // 统计信息获取方法
        public long getCompositionCount() {
            return compositionCount.sum();
        }

        public long getFileTransferCount() {
            return fileTransferCount.sum();
        }

        public long getFileTransferBytes() {
            return fileTransferBytes.sum();
        }

        public double getAverageFileTransferTime() {
            long count = fileTransferCount.sum();
            return count > 0 ? (double) fileTransferTime.sum() / count / 1_000_000 : 0; // ms
        }

        public long getMemoryMappingCount() {
            return memoryMappingCount.sum();
        }

        public long getMemoryMappingBytes() {
            return memoryMappingBytes.sum();
        }

        public double getAverageMemoryMappingTime() {
            long count = memoryMappingCount.sum();
            return count > 0 ? (double) memoryMappingTime.sum() / count / 1_000_000 : 0; // ms
        }

        public long getBufferMergeCount() {
            return bufferMergeCount.sum();
        }

        public long getBufferMergeBytes() {
            return bufferMergeBytes.sum();
        }

        public long getBufferSplitCount() {
            return bufferSplitCount.sum();
        }

        public long getTransferErrorCount() {
            return transferErrorCount.sum();
        }

        public double getFileTransferThroughputMBps() {
            long totalBytes = fileTransferBytes.sum();
            long totalTimeMs = fileTransferTime.sum() / 1_000_000;
            if (totalTimeMs > 0) {
                return (double) totalBytes / 1024 / 1024 / (totalTimeMs / 1000.0);
            }
            return 0;
        }

        @Override
        public String toString() {
            return String.format(
                "TransferStats{compositions=%d, fileTransfers=%d(%.2fMB, %.2fMB/s), " +
                "memoryMappings=%d(%.2fMB), bufferOps=%d/%d, errors=%d}",
                getCompositionCount(),
                getFileTransferCount(),
                getFileTransferBytes() / 1024.0 / 1024.0,
                getFileTransferThroughputMBps(),
                getMemoryMappingCount(),
                getMemoryMappingBytes() / 1024.0 / 1024.0,
                getBufferMergeCount(),
                getBufferSplitCount(),
                getTransferErrorCount()
            );
        }
    }
}