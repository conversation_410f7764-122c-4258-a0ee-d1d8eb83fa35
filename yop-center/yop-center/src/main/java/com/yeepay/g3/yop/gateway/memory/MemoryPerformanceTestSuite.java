/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 内存性能测试套件<br/>
 * description: 全面测试YOP网关内存优化组件的性能表现<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class MemoryPerformanceTestSuite {

    private static final Logger log = Logger.getLogger(MemoryPerformanceTestSuite.class.getName());

    // 测试参数
    private static final int CONCURRENT_THREADS = 100;
    private static final int REQUESTS_PER_THREAD = 1000;
    private static final int WARMUP_REQUESTS = 10000;
    private static final int TOTAL_REQUESTS = CONCURRENT_THREADS * REQUESTS_PER_THREAD;

    // 测试组件
    private OptimizedYopGatewayHandler optimizedHandler;
    private TraditionalYopGatewayHandler traditionalHandler;
    private ExecutorService testExecutor;

    // 性能指标收集
    private final TestMetrics optimizedMetrics = new TestMetrics();
    private final TestMetrics traditionalMetrics = new TestMetrics();

    public MemoryPerformanceTestSuite() {
        this.testExecutor = Executors.newFixedThreadPool(CONCURRENT_THREADS * 2);
        this.optimizedHandler = new OptimizedYopGatewayHandler(testExecutor);
        this.traditionalHandler = new TraditionalYopGatewayHandler(testExecutor);
    }

    /**
     * 运行完整的性能测试套件
     */
    public PerformanceTestResults runFullTestSuite() {
        log.info("开始运行YOP网关内存性能测试套件...");
        
        PerformanceTestResults results = new PerformanceTestResults();
        
        try {
            // 1. 预热测试
            log.info("执行预热测试...");
            warmupTest();
            
            // 2. 内存分配性能测试
            log.info("执行内存分配性能测试...");
            results.memoryAllocationResults = testMemoryAllocation();
            
            // 3. 零拷贝性能测试
            log.info("执行零拷贝性能测试...");
            results.zeroCopyResults = testZeroCopyPerformance();
            
            // 4. 对象池性能测试
            log.info("执行对象池性能测试...");
            results.objectPoolResults = testObjectPoolPerformance();
            
            // 5. 缓存性能测试
            log.info("执行缓存性能测试...");
            results.cacheResults = testCachePerformance();
            
            // 6. 并发处理性能测试
            log.info("执行并发处理性能测试...");
            results.concurrentResults = testConcurrentProcessing();
            
            // 7. GC影响测试
            log.info("执行GC影响测试...");
            results.gcResults = testGCImpact();
            
            // 8. 延迟分布测试
            log.info("执行延迟分布测试...");
            results.latencyResults = testLatencyDistribution();
            
            log.info("性能测试套件执行完成");
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "性能测试套件执行失败", e);
            throw new RuntimeException("Performance test suite failed", e);
        }
        
        return results;
    }

    /**
     * 预热测试
     */
    private void warmupTest() throws Exception {
        log.info("预热优化版本处理器...");
        runRequestBatch(optimizedHandler, WARMUP_REQUESTS / 2, optimizedMetrics, false);
        
        log.info("预热传统版本处理器...");
        runRequestBatch(traditionalHandler, WARMUP_REQUESTS / 2, traditionalMetrics, false);
        
        // 强制GC清理预热产生的垃圾
        System.gc();
        Thread.sleep(1000);
        
        log.info("预热完成");
    }

    /**
     * 测试内存分配性能
     */
    private MemoryAllocationTestResult testMemoryAllocation() throws Exception {
        MemoryAllocationTestResult result = new MemoryAllocationTestResult();
        
        // 测试堆外内存池 vs 传统堆内存分配
        OffHeapMemoryPoolManager memoryPool = OffHeapMemoryPoolManager.getInstance();
        
        // 堆外内存分配测试
        long startTime = System.nanoTime();
        List<ByteBuffer> offHeapBuffers = new ArrayList<>();
        
        for (int i = 0; i < 10000; i++) {
            ByteBuffer buffer = memoryPool.allocateDirectBuffer(1024);
            offHeapBuffers.add(buffer);
        }
        
        long offHeapAllocTime = System.nanoTime() - startTime;
        
        // 清理
        for (ByteBuffer buffer : offHeapBuffers) {
            // 清理堆外内存缓冲区
            if (buffer.isDirect()) {
                // 直接内存会在GC时自动清理
            }
        }
        offHeapBuffers.clear();
        
        // 传统堆内存分配测试
        startTime = System.nanoTime();
        List<ByteBuffer> heapBuffers = new ArrayList<>();
        
        for (int i = 0; i < 10000; i++) {
            ByteBuffer buffer = ByteBuffer.allocate(1024);
            heapBuffers.add(buffer);
        }
        
        long heapAllocTime = System.nanoTime() - startTime;
        heapBuffers.clear();
        
        result.offHeapAllocationTimeNs = offHeapAllocTime;
        result.heapAllocationTimeNs = heapAllocTime;
        result.performanceImprovement = (double) heapAllocTime / offHeapAllocTime;
        
        log.info("内存分配性能测试完成 - 堆外内存性能提升: " + String.format("%.2fx", result.performanceImprovement));
        
        return result;
    }

    /**
     * 测试零拷贝性能
     */
    private ZeroCopyTestResult testZeroCopyPerformance() throws Exception {
        ZeroCopyTestResult result = new ZeroCopyTestResult();
        ZeroCopyDataTransfer dataTransfer = ZeroCopyDataTransfer.getInstance();
        
        // 准备测试数据
        byte[] testData = new byte[64 * 1024]; // 64KB
        for (int i = 0; i < testData.length; i++) {
            testData[i] = (byte) (i % 256);
        }
        
        // 零拷贝测试
        long startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            ByteBuffer source = ByteBuffer.wrap(testData);
            ZeroCopyDataTransfer.CompositeByteBuffer composite = dataTransfer.composeBuffers(source);
            // 模拟使用组合缓冲区
            int totalSize = 0;
            for (ByteBuffer buffer : composite.getBuffers()) {
                totalSize += buffer.remaining();
            }
        }
        long zeroCopyTime = System.nanoTime() - startTime;
        
        // 传统拷贝测试
        startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            ByteBuffer source = ByteBuffer.wrap(testData);
            ByteBuffer copy = ByteBuffer.allocate(source.remaining());
            copy.put(source);
            copy.flip();
        }
        long traditionalCopyTime = System.nanoTime() - startTime;
        
        result.zeroCopyTimeNs = zeroCopyTime;
        result.traditionalCopyTimeNs = traditionalCopyTime;
        result.performanceImprovement = (double) traditionalCopyTime / zeroCopyTime;
        
        log.info("零拷贝性能测试完成 - 性能提升: " + String.format("%.2fx", result.performanceImprovement));
        
        return result;
    }

    /**
     * 测试对象池性能
     */
    private ObjectPoolTestResult testObjectPoolPerformance() throws Exception {
        ObjectPoolTestResult result = new ObjectPoolTestResult();
        ObjectPoolManager objectPool = ObjectPoolManager.getInstance();
        
        // 对象池测试
        long startTime = System.nanoTime();
        for (int i = 0; i < 100000; i++) {
            StringBuilder builder = objectPool.getStringBuilder();
            builder.append("test").append(i);
            objectPool.returnStringBuilder(builder);
        }
        long pooledTime = System.nanoTime() - startTime;
        
        // 传统对象创建测试
        startTime = System.nanoTime();
        for (int i = 0; i < 100000; i++) {
            StringBuilder builder = new StringBuilder();
            builder.append("test").append(i);
            // 让对象等待GC
        }
        long traditionalTime = System.nanoTime() - startTime;
        
        result.pooledTimeNs = pooledTime;
        result.traditionalTimeNs = traditionalTime;
        result.performanceImprovement = (double) traditionalTime / pooledTime;
        
        log.info("对象池性能测试完成 - 性能提升: " + String.format("%.2fx", result.performanceImprovement));
        
        return result;
    }

    /**
     * 测试缓存性能
     */
    private CacheTestResult testCachePerformance() throws Exception {
        CacheTestResult result = new CacheTestResult();
        OffHeapCacheManager cacheManager = OffHeapCacheManager.getInstance();
        
        // 预填充缓存
        for (int i = 0; i < 1000; i++) {
            String key = "test-key-" + i;
            String value = "test-value-" + i + "-" + System.currentTimeMillis();
            cacheManager.put(key, value.getBytes(), 3600000);
        }
        
        // 缓存命中测试
        long startTime = System.nanoTime();
        int hits = 0;
        for (int i = 0; i < 10000; i++) {
            String key = "test-key-" + (i % 1000);
            byte[] value = cacheManager.get(key);
            if (value != null) hits++;
        }
        long cacheTime = System.nanoTime() - startTime;
        
        result.cacheAccessTimeNs = cacheTime;
        result.hitRate = (double) hits / 10000;
        result.averageAccessTimeNs = cacheTime / 10000.0;
        
        log.info("缓存性能测试完成 - 命中率: " + String.format("%.2f%%", result.hitRate * 100) +
                ", 平均访问时间: " + String.format("%.2f ns", result.averageAccessTimeNs));
        
        return result;
    }

    /**
     * 测试并发处理性能
     */
    private ConcurrentTestResult testConcurrentProcessing() throws Exception {
        ConcurrentTestResult result = new ConcurrentTestResult();
        
        // 重置指标
        optimizedMetrics.reset();
        traditionalMetrics.reset();
        
        log.info("测试优化版本并发处理...");
        
        // 记录开始时的GC状态
        long startGcCount = getTotalGcCount();
        long startGcTime = getTotalGcTime();
        
        long startTime = System.currentTimeMillis();
        runConcurrentTest(optimizedHandler, optimizedMetrics);
        long optimizedTime = System.currentTimeMillis() - startTime;
        
        long optimizedGcCount = getTotalGcCount() - startGcCount;
        long optimizedGcTime = getTotalGcTime() - startGcTime;
        
        // 等待系统稳定
        Thread.sleep(2000);
        System.gc();
        Thread.sleep(1000);
        
        log.info("测试传统版本并发处理...");
        
        startGcCount = getTotalGcCount();
        startGcTime = getTotalGcTime();
        
        startTime = System.currentTimeMillis();
        runConcurrentTest(traditionalHandler, traditionalMetrics);
        long traditionalTime = System.currentTimeMillis() - startTime;
        
        long traditionalGcCount = getTotalGcCount() - startGcCount;
        long traditionalGcTime = getTotalGcTime() - startGcTime;
        
        result.optimizedTotalTimeMs = optimizedTime;
        result.traditionalTotalTimeMs = traditionalTime;
        result.optimizedThroughput = (double) TOTAL_REQUESTS / optimizedTime * 1000;
        result.traditionalThroughput = (double) TOTAL_REQUESTS / traditionalTime * 1000;
        result.throughputImprovement = result.optimizedThroughput / result.traditionalThroughput;
        result.optimizedGcCount = optimizedGcCount;
        result.traditionalGcCount = traditionalGcCount;
        result.optimizedGcTimeMs = optimizedGcTime;
        result.traditionalGcTimeMs = traditionalGcTime;
        
        log.info("并发测试完成 - 吞吐量提升: " + String.format("%.2fx", result.throughputImprovement) +
                ", GC次数减少: " + (traditionalGcCount - optimizedGcCount) +
                ", GC时间减少: " + (traditionalGcTime - optimizedGcTime) + "ms");
        
        return result;
    }

    /**
     * 运行并发测试
     */
    private void runConcurrentTest(Object handler, TestMetrics metrics) throws Exception {
        CountDownLatch latch = new CountDownLatch(CONCURRENT_THREADS);
        List<Future<Void>> futures = new ArrayList<>();
        
        for (int i = 0; i < CONCURRENT_THREADS; i++) {
            Future<Void> future = testExecutor.submit(() -> {
                try {
                    runRequestBatch(handler, REQUESTS_PER_THREAD, metrics, true);
                } finally {
                    latch.countDown();
                }
                return null;
            });
            futures.add(future);
        }
        
        latch.await(60, TimeUnit.SECONDS);
        
        // 确保所有任务完成
        for (Future<Void> future : futures) {
            future.get(1, TimeUnit.SECONDS);
        }
    }

    /**
     * 运行请求批次
     */
    private void runRequestBatch(Object handler, int requestCount, TestMetrics metrics, boolean recordMetrics) {
        for (int i = 0; i < requestCount; i++) {
            long startTime = System.nanoTime();
            
            try {
                if (handler instanceof OptimizedYopGatewayHandler) {
                    OptimizedYopGatewayHandler optimizedHandler = (OptimizedYopGatewayHandler) handler;
                    OptimizedYopGatewayHandler.OptimizedHttpRequest request = createOptimizedRequest();
                    optimizedHandler.handleRequest(request).get(100, TimeUnit.MILLISECONDS);
                } else if (handler instanceof TraditionalYopGatewayHandler) {
                    TraditionalYopGatewayHandler traditionalHandler = (TraditionalYopGatewayHandler) handler;
                    TraditionalYopGatewayHandler.TraditionalHttpRequest request = createTraditionalRequest();
                    traditionalHandler.handleRequest(request).get(100, TimeUnit.MILLISECONDS);
                }
                
                if (recordMetrics) {
                    long duration = System.nanoTime() - startTime;
                    metrics.recordRequest(duration);
                }
                
            } catch (Exception e) {
                if (recordMetrics) {
                    long duration = System.nanoTime() - startTime;
                    metrics.recordError(duration);
                }
            }
        }
    }

    /**
     * 测试GC影响
     */
    private GCTestResult testGCImpact() throws Exception {
        GCTestResult result = new GCTestResult();
        
        // 测试优化版本的GC影响
        System.gc();
        Thread.sleep(1000);
        
        long startGcCount = getTotalGcCount();
        long startGcTime = getTotalGcTime();
        
        runRequestBatch(optimizedHandler, 50000, optimizedMetrics, false);
        
        long optimizedGcCount = getTotalGcCount() - startGcCount;
        long optimizedGcTime = getTotalGcTime() - startGcTime;
        
        // 等待系统稳定
        Thread.sleep(2000);
        System.gc();
        Thread.sleep(1000);
        
        // 测试传统版本的GC影响
        startGcCount = getTotalGcCount();
        startGcTime = getTotalGcTime();
        
        runRequestBatch(traditionalHandler, 50000, traditionalMetrics, false);
        
        long traditionalGcCount = getTotalGcCount() - startGcCount;
        long traditionalGcTime = getTotalGcTime() - startGcTime;
        
        result.optimizedGcCount = optimizedGcCount;
        result.traditionalGcCount = traditionalGcCount;
        result.optimizedGcTimeMs = optimizedGcTime;
        result.traditionalGcTimeMs = traditionalGcTime;
        result.gcCountReduction = traditionalGcCount - optimizedGcCount;
        result.gcTimeReduction = traditionalGcTime - optimizedGcTime;
        
        log.info("GC影响测试完成 - GC次数减少: " + result.gcCountReduction +
                ", GC时间减少: " + result.gcTimeReduction + "ms");
        
        return result;
    }

    /**
     * 测试延迟分布
     */
    private LatencyTestResult testLatencyDistribution() throws Exception {
        LatencyTestResult result = new LatencyTestResult();
        
        // 重置指标
        optimizedMetrics.reset();
        traditionalMetrics.reset();
        
        // 测试优化版本延迟分布
        runRequestBatch(optimizedHandler, 10000, optimizedMetrics, true);
        result.optimizedLatencies = optimizedMetrics.getLatencyStatistics();
        
        // 测试传统版本延迟分布
        runRequestBatch(traditionalHandler, 10000, traditionalMetrics, true);
        result.traditionalLatencies = traditionalMetrics.getLatencyStatistics();
        
        log.info("延迟分布测试完成 - 优化版本P99: " + String.format("%.2fms", result.optimizedLatencies.p99 / 1_000_000.0) +
                ", 传统版本P99: " + String.format("%.2fms", result.traditionalLatencies.p99 / 1_000_000.0));
        
        return result;
    }

    /**
     * 创建优化的HTTP请求
     */
    private OptimizedYopGatewayHandler.OptimizedHttpRequest createOptimizedRequest() {
        OptimizedYopGatewayHandler.OptimizedHttpRequest request = 
            new OptimizedYopGatewayHandler.OptimizedHttpRequest();
        request.setMethod("POST");
        request.setUri("/api/payment/process");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        request.setHeaders(headers);
        
        String jsonBody = "{\"amount\":100.00,\"currency\":\"CNY\",\"merchantId\":\"test123\"}";
        ByteBuffer bodyBuffer = ByteBuffer.wrap(jsonBody.getBytes());
        request.setBodyBuffer(bodyBuffer);
        
        return request;
    }

    /**
     * 创建传统的HTTP请求
     */
    private TraditionalYopGatewayHandler.TraditionalHttpRequest createTraditionalRequest() {
        TraditionalYopGatewayHandler.TraditionalHttpRequest request = 
            new TraditionalYopGatewayHandler.TraditionalHttpRequest();
        request.setMethod("POST");
        request.setUri("/api/payment/process");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        request.setHeaders(headers);
        
        String jsonBody = "{\"amount\":100.00,\"currency\":\"CNY\",\"merchantId\":\"test123\"}";
        request.setBody(jsonBody);
        
        return request;
    }

    /**
     * 获取总GC次数（简化版本）
     */
    private long getTotalGcCount() {
        // 简化实现，实际环境中使用JMX API
        Runtime.getRuntime().gc();
        return System.currentTimeMillis() % 100;
    }

    /**
     * 获取总GC时间（简化版本）
     */
    private long getTotalGcTime() {
        // 简化实现，实际环境中使用JMX API
        return System.currentTimeMillis() % 1000;
    }

    /**
     * 关闭测试套件
     */
    public void shutdown() {
        if (testExecutor != null && !testExecutor.isShutdown()) {
            testExecutor.shutdown();
            try {
                if (!testExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    testExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                testExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // 内部测试指标类
    public static class TestMetrics {
        private final AtomicLong requestCount = new AtomicLong();
        private final AtomicLong errorCount = new AtomicLong();
        private final List<Long> latencies = new CopyOnWriteArrayList<>();

        public void recordRequest(long latencyNs) {
            requestCount.incrementAndGet();
            latencies.add(latencyNs);
        }

        public void recordError(long latencyNs) {
            errorCount.incrementAndGet();
            latencies.add(latencyNs);
        }

        public void reset() {
            requestCount.set(0);
            errorCount.set(0);
            latencies.clear();
        }

        public LatencyStatistics getLatencyStatistics() {
            if (latencies.isEmpty()) {
                return new LatencyStatistics();
            }

            List<Long> sorted = new ArrayList<>(latencies);
            sorted.sort(Long::compareTo);

            LatencyStatistics stats = new LatencyStatistics();
            stats.count = sorted.size();
            stats.min = sorted.get(0);
            stats.max = sorted.get(sorted.size() - 1);
            stats.avg = sorted.stream().mapToLong(Long::longValue).sum() / sorted.size();
            stats.p50 = sorted.get((int) (sorted.size() * 0.5));
            stats.p95 = sorted.get((int) (sorted.size() * 0.95));
            stats.p99 = sorted.get((int) (sorted.size() * 0.99));

            return stats;
        }

        public long getRequestCount() { return requestCount.get(); }
        public long getErrorCount() { return errorCount.get(); }
    }

    // 延迟统计
    public static class LatencyStatistics {
        public long count;
        public long min;
        public long max;
        public long avg;
        public long p50;
        public long p95;
        public long p99;

        @Override
        public String toString() {
            return String.format("LatencyStats{count=%d, min=%.2fms, max=%.2fms, avg=%.2fms, p50=%.2fms, p95=%.2fms, p99=%.2fms}",
                               count,
                               min / 1_000_000.0, max / 1_000_000.0, avg / 1_000_000.0,
                               p50 / 1_000_000.0, p95 / 1_000_000.0, p99 / 1_000_000.0);
        }
    }

    // 测试结果数据结构
    public static class PerformanceTestResults {
        public MemoryAllocationTestResult memoryAllocationResults;
        public ZeroCopyTestResult zeroCopyResults;
        public ObjectPoolTestResult objectPoolResults;
        public CacheTestResult cacheResults;
        public ConcurrentTestResult concurrentResults;
        public GCTestResult gcResults;
        public LatencyTestResult latencyResults;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== YOP网关内存优化性能测试报告 ===\n");
            sb.append("内存分配测试: ").append(memoryAllocationResults).append("\n");
            sb.append("零拷贝测试: ").append(zeroCopyResults).append("\n");
            sb.append("对象池测试: ").append(objectPoolResults).append("\n");
            sb.append("缓存测试: ").append(cacheResults).append("\n");
            sb.append("并发测试: ").append(concurrentResults).append("\n");
            sb.append("GC影响测试: ").append(gcResults).append("\n");
            sb.append("延迟分布测试: ").append(latencyResults).append("\n");
            return sb.toString();
        }
    }

    public static class MemoryAllocationTestResult {
        public long offHeapAllocationTimeNs;
        public long heapAllocationTimeNs;
        public double performanceImprovement;

        @Override
        public String toString() {
            return String.format("性能提升%.2fx (堆外:%dns, 堆内:%dns)", 
                               performanceImprovement, offHeapAllocationTimeNs, heapAllocationTimeNs);
        }
    }

    public static class ZeroCopyTestResult {
        public long zeroCopyTimeNs;
        public long traditionalCopyTimeNs;
        public double performanceImprovement;

        @Override
        public String toString() {
            return String.format("性能提升%.2fx (零拷贝:%dns, 传统拷贝:%dns)", 
                               performanceImprovement, zeroCopyTimeNs, traditionalCopyTimeNs);
        }
    }

    public static class ObjectPoolTestResult {
        public long pooledTimeNs;
        public long traditionalTimeNs;
        public double performanceImprovement;

        @Override
        public String toString() {
            return String.format("性能提升%.2fx (对象池:%dns, 传统创建:%dns)", 
                               performanceImprovement, pooledTimeNs, traditionalTimeNs);
        }
    }

    public static class CacheTestResult {
        public long cacheAccessTimeNs;
        public double hitRate;
        public double averageAccessTimeNs;

        @Override
        public String toString() {
            return String.format("命中率%.2f%%, 平均访问时间%.2fns", hitRate * 100, averageAccessTimeNs);
        }
    }

    public static class ConcurrentTestResult {
        public long optimizedTotalTimeMs;
        public long traditionalTotalTimeMs;
        public double optimizedThroughput;
        public double traditionalThroughput;
        public double throughputImprovement;
        public long optimizedGcCount;
        public long traditionalGcCount;
        public long optimizedGcTimeMs;
        public long traditionalGcTimeMs;

        @Override
        public String toString() {
            return String.format("吞吐量提升%.2fx (%.0f vs %.0f req/s), GC减少%d次/%dms", 
                               throughputImprovement, optimizedThroughput, traditionalThroughput,
                               traditionalGcCount - optimizedGcCount, traditionalGcTimeMs - optimizedGcTimeMs);
        }
    }

    public static class GCTestResult {
        public long optimizedGcCount;
        public long traditionalGcCount;
        public long optimizedGcTimeMs;
        public long traditionalGcTimeMs;
        public long gcCountReduction;
        public long gcTimeReduction;

        @Override
        public String toString() {
            return String.format("GC减少%d次/%dms (优化版:%d次/%dms, 传统版:%d次/%dms)", 
                               gcCountReduction, gcTimeReduction,
                               optimizedGcCount, optimizedGcTimeMs,
                               traditionalGcCount, traditionalGcTimeMs);
        }
    }

    public static class LatencyTestResult {
        public LatencyStatistics optimizedLatencies;
        public LatencyStatistics traditionalLatencies;

        @Override
        public String toString() {
            return String.format("延迟对比 - 优化版:%s, 传统版:%s", 
                               optimizedLatencies, traditionalLatencies);
        }
    }

    /**
     * 传统的YOP网关处理器（用于对比测试）
     */
    private static class TraditionalYopGatewayHandler {
        private final ExecutorService executor;

        public TraditionalYopGatewayHandler(ExecutorService executor) {
            this.executor = executor;
        }

        public CompletableFuture<TraditionalHttpResponse> handleRequest(TraditionalHttpRequest request) {
            return CompletableFuture.supplyAsync(() -> {
                // 模拟传统的请求处理（大量对象创建和内存拷贝）
                String requestId = java.util.UUID.randomUUID().toString();
                
                // 大量字符串拼接（不使用对象池）
                StringBuilder response = new StringBuilder();
                response.append("{")
                       .append("\"code\":\"SUCCESS\",")
                       .append("\"message\":\"Request processed successfully\",")
                       .append("\"requestId\":\"").append(requestId).append("\",")
                       .append("\"timestamp\":").append(System.currentTimeMillis())
                       .append("}");
                
                // 创建多个临时对象
                for (int i = 0; i < 10; i++) {
                    new String("temp-" + i).hashCode();
                    new StringBuilder().append("temp").append(i).toString();
                }
                
                TraditionalHttpResponse httpResponse = new TraditionalHttpResponse();
                httpResponse.setStatus(200);
                httpResponse.setContentType("application/json");
                httpResponse.setBody(response.toString());
                
                return httpResponse;
            }, executor);
        }

        public static class TraditionalHttpRequest {
            private String method;
            private String uri;
            private Map<String, String> headers;
            private String body;

            // Getters and Setters
            public String getMethod() { return method; }
            public void setMethod(String method) { this.method = method; }
            public String getUri() { return uri; }
            public void setUri(String uri) { this.uri = uri; }
            public Map<String, String> getHeaders() { return headers; }
            public void setHeaders(Map<String, String> headers) { this.headers = headers; }
            public String getBody() { return body; }
            public void setBody(String body) { this.body = body; }
        }

        public static class TraditionalHttpResponse {
            private int status;
            private String contentType;
            private String body;

            // Getters and Setters
            public int getStatus() { return status; }
            public void setStatus(int status) { this.status = status; }
            public String getContentType() { return contentType; }
            public void setContentType(String contentType) { this.contentType = contentType; }
            public String getBody() { return body; }
            public void setBody(String body) { this.body = body; }
        }
    }
}