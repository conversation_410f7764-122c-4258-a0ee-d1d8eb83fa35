/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.backend.async;

import com.yeepay.g3.yop.ext.gateway.server.AsyncServerWebExchange;
import com.yeepay.g3.yop.gateway.router.PredicateInputGenerator;
import com.yeepay.g3.yop.router.predicate.Predicate;
import com.yeepay.g3.yop.router.predicate.PredicateInput;
import com.yeepay.g3.yop.router.route.Router;
import com.yeepay.g3.yop.router.route.RouterWithPredicate;
import com.yeepay.g3.yop.router.utils.RouteConstants;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * title: 异步路由后端实现<br/>
 * description: 支持异步路由和负载均衡的后端实现<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class AsyncRouterBackend extends AbstractAsyncApiBackend {

    private static final Logger log = Logger.getLogger(AsyncRouterBackend.class.getName());

    // 路由谓词列表
    private List<RouterWithPredicate> routerWithPredicates;
    
    // 谓词输入生成器
    private PredicateInputGenerator predicateInputGenerator;
    
    // 负载均衡器
    private LoadBalancer loadBalancer;

    public AsyncRouterBackend() {
        super();
        this.loadBalancer = new RoundRobinLoadBalancer();
    }

    @Override
    protected CompletableFuture<Object> doInvokeAsync(AsyncServerWebExchange exchange) {
        return executeInBusinessPool(() -> {
            try {
                // 生成谓词输入
                PredicateInput predicateInput = predicateInputGenerator.generateInput(exchange);
                
                // 选择匹配的路由
                Router selectedRouter = selectRouter(predicateInput);
                
                if (selectedRouter == null) {
                    throw new RuntimeException("No matching router found");
                }
                
                // 执行路由调用
                return executeRouterAsync(selectedRouter, exchange);
                
            } catch (Exception e) {
                log.log(Level.SEVERE, "Failed to execute async router", e);
                throw new RuntimeException("Router execution failed", e);
            }
        }).thenCompose(future -> (CompletableFuture<Object>) future);
    }

    /**
     * 选择匹配的路由
     */
    private Router selectRouter(PredicateInput predicateInput) {
        if (routerWithPredicates == null || routerWithPredicates.isEmpty()) {
            return null;
        }
        
        int size = routerWithPredicates.size();
        
        // 使用负载均衡器选择路由
        if (getLoadBalanceStrategy() != LoadBalanceStrategy.ROUND_ROBIN) {
            return loadBalancer.select(routerWithPredicates, predicateInput);
        }
        
        // 原始的谓词匹配逻辑
        for (int i = 0; i < size; i++) {
            RouterWithPredicate routerWithPredicate = routerWithPredicates.get(i);
            Predicate predicate = routerWithPredicate.getPredicate();
            
            // 没有谓词直接通过，最后一个直接通过，或者谓词匹配成功
            if (predicate == null || i == size - 1 || predicate.apply(predicateInput)) {
                return routerWithPredicate.getRouter();
            }
        }
        
        return null;
    }

    /**
     * 异步执行路由调用
     */
    private CompletableFuture<Object> executeRouterAsync(Router router, AsyncServerWebExchange exchange) {
        return executeInIOPool(() -> {
            try {
                // 创建路由上下文（从exchange中获取）
                Object routeContext = getRouteContext(exchange);
                
                // 执行路由调用
                Object response = router.route(routeContext);
                
                // 执行响应映射
                Map<String, Object> responseMap = router.responseMapping(buildResponse(response));
                
                return responseMap.get(RouteConstants.BODY);
                
            } catch (Exception e) {
                log.log(Level.SEVERE, "Router execution failed", e);
                throw new RuntimeException("Router execution failed", e);
            }
        });
    }

    /**
     * 从AsyncServerWebExchange获取路由上下文
     */
    private Object getRouteContext(AsyncServerWebExchange exchange) {
        // 从exchange的attributes中获取路由上下文
        return exchange.getAttribute("routeContext");
    }

    /**
     * 构建响应对象
     */
    private Map<String, Object> buildResponse(Object rawResponse) {
        Map<String, Object> result = new HashMap<>();
        result.put(RouteConstants.BODY, rawResponse);
        return result;
    }

    @Override
    protected boolean doHealthCheck() {
        if (routerWithPredicates == null || routerWithPredicates.isEmpty()) {
            return false;
        }
        
        // 检查每个路由的健康状态
        for (RouterWithPredicate routerWithPredicate : routerWithPredicates) {
            Router router = routerWithPredicate.getRouter();
            if (router == null) {
                return false;
            }
            // 这里可以添加更详细的路由健康检查逻辑
        }
        
        return true;
    }

    @Override
    public BackendType getBackendType() {
        return BackendType.ROUTER;
    }

    @Override
    public boolean supportsHealthCheck() {
        return true;
    }

    @Override
    public boolean supportsRetry() {
        return true;
    }

    @Override
    public boolean supportsCircuitBreaker() {
        return true;
    }

    // Getters and Setters
    public List<RouterWithPredicate> getRouterWithPredicates() {
        return routerWithPredicates;
    }

    public void setRouterWithPredicates(List<RouterWithPredicate> routerWithPredicates) {
        this.routerWithPredicates = routerWithPredicates;
    }

    public PredicateInputGenerator getPredicateInputGenerator() {
        return predicateInputGenerator;
    }

    public void setPredicateInputGenerator(PredicateInputGenerator predicateInputGenerator) {
        this.predicateInputGenerator = predicateInputGenerator;
    }

    public LoadBalancer getLoadBalancer() {
        return loadBalancer;
    }

    public void setLoadBalancer(LoadBalancer loadBalancer) {
        this.loadBalancer = loadBalancer;
    }

    /**
     * 负载均衡器接口
     */
    public interface LoadBalancer {
        Router select(List<RouterWithPredicate> routers, PredicateInput predicateInput);
    }

    /**
     * 轮询负载均衡器
     */
    public static class RoundRobinLoadBalancer implements LoadBalancer {
        private volatile int currentIndex = 0;

        @Override
        public Router select(List<RouterWithPredicate> routers, PredicateInput predicateInput) {
            if (routers == null || routers.isEmpty()) {
                return null;
            }
            
            int index = Math.abs(currentIndex++ % routers.size());
            return routers.get(index).getRouter();
        }
    }

    /**
     * 加权轮询负载均衡器
     */
    public static class WeightedRoundRobinLoadBalancer implements LoadBalancer {
        private final Map<Router, Integer> weights = new HashMap<>();
        private final Map<Router, Integer> currentWeights = new HashMap<>();
        
        @Override
        public Router select(List<RouterWithPredicate> routers, PredicateInput predicateInput) {
            if (routers == null || routers.isEmpty()) {
                return null;
            }
            
            Router selected = null;
            int totalWeight = 0;
            
            for (RouterWithPredicate routerWithPredicate : routers) {
                Router router = routerWithPredicate.getRouter();
                int weight = weights.getOrDefault(router, 1);
                int currentWeight = currentWeights.getOrDefault(router, 0) + weight;
                
                currentWeights.put(router, currentWeight);
                totalWeight += weight;
                
                if (selected == null || currentWeight > currentWeights.get(selected)) {
                    selected = router;
                }
            }
            
            if (selected != null) {
                currentWeights.put(selected, 
                    currentWeights.get(selected) - totalWeight);
            }
            
            return selected;
        }
        
        public void setWeight(Router router, int weight) {
            weights.put(router, weight);
        }
    }

    /**
     * 最少连接负载均衡器
     */
    public static class LeastConnectionsLoadBalancer implements LoadBalancer {
        private final Map<Router, Integer> connections = new HashMap<>();
        
        @Override
        public Router select(List<RouterWithPredicate> routers, PredicateInput predicateInput) {
            if (routers == null || routers.isEmpty()) {
                return null;
            }
            
            Router selected = null;
            int minConnections = Integer.MAX_VALUE;
            
            for (RouterWithPredicate routerWithPredicate : routers) {
                Router router = routerWithPredicate.getRouter();
                int connectionCount = connections.getOrDefault(router, 0);
                
                if (connectionCount < minConnections) {
                    minConnections = connectionCount;
                    selected = router;
                }
            }
            
            if (selected != null) {
                connections.put(selected, connections.getOrDefault(selected, 0) + 1);
            }
            
            return selected;
        }
        
        public void releaseConnection(Router router) {
            connections.computeIfPresent(router, (k, v) -> Math.max(0, v - 1));
        }
    }

    @Override
    public void close() throws Exception {
        super.close();

        // 清理路由相关资源
        if (routerWithPredicates != null) {
            routerWithPredicates.clear();
        }

        // 清理负载均衡器
        if (loadBalancer instanceof LeastConnectionsLoadBalancer) {
            ((LeastConnectionsLoadBalancer) loadBalancer).connections.clear();
        }

        log.info("AsyncRouterBackend closed: " + getName());
    }
}