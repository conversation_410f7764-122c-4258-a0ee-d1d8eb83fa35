/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 堆外缓存管理器<br/>
 * description: 高性能堆外内存缓存，支持LRU淘汰和预热机制，减少GC压力<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class OffHeapCacheManager {

    private static final Logger log = Logger.getLogger(OffHeapCacheManager.class.getName());
    private static final OffHeapCacheManager INSTANCE = new OffHeapCacheManager();

    // 缓存存储
    private final ConcurrentHashMap<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    // 内存池管理器
    private final OffHeapMemoryPoolManager memoryPool = OffHeapMemoryPoolManager.getInstance();
    
    // LRU链表头尾节点
    private volatile CacheEntry head;
    private volatile CacheEntry tail;
    
    // 缓存统计
    private final CacheStats stats = new CacheStats();
    
    // 配置
    private final CacheConfig config = new CacheConfig();
    
    // 调度器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2, 
        r -> {
            Thread t = new Thread(r, "offheap-cache-manager");
            t.setDaemon(true);
            return t;
        });
    
    // 缓存大小控制
    private final AtomicLong currentSize = new AtomicLong(0);
    private final AtomicLong currentMemoryUsage = new AtomicLong(0);

    private OffHeapCacheManager() {
        initializeLRUList();
        startMaintenanceTasks();
        log.info("OffHeapCacheManager initialized");
    }

    public static OffHeapCacheManager getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化LRU双向链表
     */
    private void initializeLRUList() {
        head = new CacheEntry("__HEAD__", null, 0);
        tail = new CacheEntry("__TAIL__", null, 0);
        head.next = tail;
        tail.prev = head;
    }

    /**
     * 启动维护任务
     */
    private void startMaintenanceTasks() {
        // 定期清理过期缓存
        scheduler.scheduleAtFixedRate(this::cleanupExpiredEntries, 
                                    config.getCleanupIntervalMs(), 
                                    config.getCleanupIntervalMs(), 
                                    TimeUnit.MILLISECONDS);
        
        // 定期检查内存使用情况
        scheduler.scheduleAtFixedRate(this::checkMemoryUsage, 
                                    config.getMemoryCheckIntervalMs(), 
                                    config.getMemoryCheckIntervalMs(), 
                                    TimeUnit.MILLISECONDS);
    }

    /**
     * 存储数据到缓存
     */
    public void put(String key, byte[] data) {
        put(key, data, config.getDefaultTTLMs());
    }

    /**
     * 存储数据到缓存（带过期时间）
     */
    public void put(String key, byte[] data, long ttlMs) {
        if (key == null || data == null) {
            return;
        }

        // 检查是否超过最大容量
        if (currentSize.get() >= config.getMaxEntries()) {
            evictLRU();
        }

        // 检查内存使用是否超限
        long dataSize = data.length;
        if (currentMemoryUsage.get() + dataSize > config.getMaxMemoryBytes()) {
            evictByMemory(dataSize);
        }

        try {
            // 分配堆外内存
            ByteBuffer buffer = memoryPool.allocateDirectBuffer(data.length);
            buffer.put(data);
            buffer.flip();

            long expirationTime = ttlMs > 0 ? System.currentTimeMillis() + ttlMs : Long.MAX_VALUE;
            CacheEntry entry = new CacheEntry(key, buffer, expirationTime);

            // 移除旧条目（如果存在）
            CacheEntry oldEntry = cache.put(key, entry);
            if (oldEntry != null) {
                removeFromLRU(oldEntry);
                releaseEntry(oldEntry);
                currentSize.decrementAndGet();
                currentMemoryUsage.addAndGet(-oldEntry.getDataSize());
            } else {
                currentSize.incrementAndGet();
            }

            // 添加到LRU链表头部
            addToHead(entry);
            currentMemoryUsage.addAndGet(dataSize);

            stats.recordPut();

        } catch (Exception e) {
            log.log(Level.WARNING, "Failed to put cache entry: " + key, e);
            stats.recordError();
        }
    }

    /**
     * 从缓存获取数据
     */
    public byte[] get(String key) {
        if (key == null) {
            return null;
        }

        CacheEntry entry = cache.get(key);
        if (entry == null) {
            stats.recordMiss();
            return null;
        }

        // 检查是否过期
        if (entry.isExpired()) {
            remove(key);
            stats.recordMiss();
            return null;
        }

        // 移动到LRU链表头部
        moveToHead(entry);
        
        // 复制数据到字节数组
        ByteBuffer buffer = entry.getData();
        if (buffer == null) {
            stats.recordMiss();
            return null;
        }

        byte[] result = new byte[buffer.remaining()];
        buffer.duplicate().get(result);
        
        stats.recordHit();
        return result;
    }

    /**
     * 存储字符串到缓存
     */
    public void putString(String key, String value) {
        putString(key, value, config.getDefaultTTLMs());
    }

    /**
     * 存储字符串到缓存（带过期时间）
     */
    public void putString(String key, String value, long ttlMs) {
        if (value != null) {
            put(key, value.getBytes(StandardCharsets.UTF_8), ttlMs);
        }
    }

    /**
     * 从缓存获取字符串
     */
    public String getString(String key) {
        byte[] data = get(key);
        return data != null ? new String(data, StandardCharsets.UTF_8) : null;
    }

    /**
     * 移除缓存条目
     */
    public boolean remove(String key) {
        CacheEntry entry = cache.remove(key);
        if (entry != null) {
            removeFromLRU(entry);
            releaseEntry(entry);
            currentSize.decrementAndGet();
            currentMemoryUsage.addAndGet(-entry.getDataSize());
            stats.recordRemove();
            return true;
        }
        return false;
    }

    /**
     * 检查缓存中是否存在指定key
     */
    public boolean containsKey(String key) {
        CacheEntry entry = cache.get(key);
        return entry != null && !entry.isExpired();
    }

    /**
     * 获取或计算缓存值
     */
    public byte[] getOrCompute(String key, Function<String, byte[]> computer) {
        byte[] cached = get(key);
        if (cached != null) {
            return cached;
        }

        // 计算新值
        byte[] computed = computer.apply(key);
        if (computed != null) {
            put(key, computed);
        }
        
        return computed;
    }

    /**
     * 获取或计算字符串缓存值
     */
    public String getOrComputeString(String key, Function<String, String> computer) {
        String cached = getString(key);
        if (cached != null) {
            return cached;
        }

        // 计算新值
        String computed = computer.apply(key);
        if (computed != null) {
            putString(key, computed);
        }
        
        return computed;
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        cache.values().forEach(this::releaseEntry);
        cache.clear();
        initializeLRUList();
        currentSize.set(0);
        currentMemoryUsage.set(0);
        stats.recordClear();
        log.info("Cache cleared");
    }

    /**
     * 预热缓存
     */
    public void warmup(CacheWarmer warmer) {
        if (warmer == null) {
            return;
        }

        log.info("Starting cache warmup");
        long startTime = System.currentTimeMillis();
        
        try {
            warmer.warmup(this);
            long duration = System.currentTimeMillis() - startTime;
            log.info("Cache warmup completed in " + duration + "ms, entries: " + currentSize.get());
        } catch (Exception e) {
            log.log(Level.WARNING, "Cache warmup failed", e);
        }
    }

    /**
     * LRU淘汰
     */
    private void evictLRU() {
        if (tail.prev != head) {
            CacheEntry toEvict = tail.prev;
            String key = toEvict.getKey();
            cache.remove(key);
            removeFromLRU(toEvict);
            releaseEntry(toEvict);
            currentSize.decrementAndGet();
            currentMemoryUsage.addAndGet(-toEvict.getDataSize());
            stats.recordEviction();
        }
    }

    /**
     * 按内存使用量淘汰
     */
    private void evictByMemory(long requiredSpace) {
        long targetMemory = config.getMaxMemoryBytes() - requiredSpace;
        
        while (currentMemoryUsage.get() > targetMemory && tail.prev != head) {
            CacheEntry toEvict = tail.prev;
            String key = toEvict.getKey();
            cache.remove(key);
            removeFromLRU(toEvict);
            long evictedSize = toEvict.getDataSize();
            releaseEntry(toEvict);
            currentSize.decrementAndGet();
            currentMemoryUsage.addAndGet(-evictedSize);
            stats.recordEviction();
        }
    }

    /**
     * 清理过期条目
     */
    private void cleanupExpiredEntries() {
        try {
            long now = System.currentTimeMillis();
            long cleaned = 0;
            
            for (String key : cache.keySet()) {
                CacheEntry entry = cache.get(key);
                if (entry != null && entry.isExpired()) {
                    if (cache.remove(key) != null) {
                        removeFromLRU(entry);
                        releaseEntry(entry);
                        currentSize.decrementAndGet();
                        currentMemoryUsage.addAndGet(-entry.getDataSize());
                        cleaned++;
                    }
                }
            }
            
            if (cleaned > 0) {
                log.fine("Cleaned up " + cleaned + " expired cache entries");
                stats.recordCleanup(cleaned);
            }
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error during cache cleanup", e);
        }
    }

    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        try {
            long memoryUsage = currentMemoryUsage.get();
            long maxMemory = config.getMaxMemoryBytes();
            double usageRatio = (double) memoryUsage / maxMemory;
            
            if (usageRatio > 0.9) {
                log.warning(String.format("High cache memory usage: %.2f%% (%d/%d bytes)", 
                           usageRatio * 100, memoryUsage, maxMemory));
                
                // 主动清理一些LRU条目
                evictByMemory(maxMemory / 10); // 清理10%的内存
            }
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error checking cache memory usage", e);
        }
    }

    /**
     * 添加到LRU链表头部
     */
    private synchronized void addToHead(CacheEntry entry) {
        entry.prev = head;
        entry.next = head.next;
        head.next.prev = entry;
        head.next = entry;
    }

    /**
     * 从LRU链表移除
     */
    private synchronized void removeFromLRU(CacheEntry entry) {
        entry.prev.next = entry.next;
        entry.next.prev = entry.prev;
    }

    /**
     * 移动到LRU链表头部
     */
    private synchronized void moveToHead(CacheEntry entry) {
        removeFromLRU(entry);
        addToHead(entry);
    }

    /**
     * 释放缓存条目
     */
    private void releaseEntry(CacheEntry entry) {
        if (entry.getData() != null) {
            memoryPool.releaseToCacheIfPossible(entry.getData());
        }
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        return stats;
    }

    /**
     * 获取缓存状态信息
     */
    public CacheStatus getStatus() {
        CacheStatus status = new CacheStatus();
        status.setEntryCount(currentSize.get());
        status.setMemoryUsage(currentMemoryUsage.get());
        status.setMaxMemory(config.getMaxMemoryBytes());
        status.setMemoryUsageRatio((double) currentMemoryUsage.get() / config.getMaxMemoryBytes());
        status.setHitRatio(stats.getHitRatio());
        return status;
    }

    /**
     * 优雅关闭
     */
    public void shutdown() {
        log.info("Shutting down OffHeapCacheManager");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        clear();
    }

    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final String key;
        private final ByteBuffer data;
        private final long expirationTime;
        private final long createTime;
        
        // LRU链表指针
        volatile CacheEntry prev;
        volatile CacheEntry next;

        public CacheEntry(String key, ByteBuffer data, long expirationTime) {
            this.key = key;
            this.data = data;
            this.expirationTime = expirationTime;
            this.createTime = System.currentTimeMillis();
        }

        public String getKey() {
            return key;
        }

        public ByteBuffer getData() {
            return data;
        }

        public boolean isExpired() {
            return expirationTime != Long.MAX_VALUE && System.currentTimeMillis() > expirationTime;
        }

        public long getDataSize() {
            return data != null ? data.capacity() : 0;
        }

        public long getAge() {
            return System.currentTimeMillis() - createTime;
        }
    }

    /**
     * 缓存预热接口
     */
    @FunctionalInterface
    public interface CacheWarmer {
        void warmup(OffHeapCacheManager cache) throws Exception;
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final LongAdder hits = new LongAdder();
        private final LongAdder misses = new LongAdder();
        private final LongAdder puts = new LongAdder();
        private final LongAdder removes = new LongAdder();
        private final LongAdder evictions = new LongAdder();
        private final LongAdder errors = new LongAdder();
        private final LongAdder clears = new LongAdder();
        private final LongAdder cleanupCount = new LongAdder();

        public void recordHit() { hits.increment(); }
        public void recordMiss() { misses.increment(); }
        public void recordPut() { puts.increment(); }
        public void recordRemove() { removes.increment(); }
        public void recordEviction() { evictions.increment(); }
        public void recordError() { errors.increment(); }
        public void recordClear() { clears.increment(); }
        public void recordCleanup(long count) { cleanupCount.add(count); }

        public long getHits() { return hits.sum(); }
        public long getMisses() { return misses.sum(); }
        public long getPuts() { return puts.sum(); }
        public long getRemoves() { return removes.sum(); }
        public long getEvictions() { return evictions.sum(); }
        public long getErrors() { return errors.sum(); }
        public long getClears() { return clears.sum(); }
        public long getCleanupCount() { return cleanupCount.sum(); }

        public double getHitRatio() {
            long totalRequests = hits.sum() + misses.sum();
            return totalRequests > 0 ? (double) hits.sum() / totalRequests : 0.0;
        }

        @Override
        public String toString() {
            return String.format("CacheStats{hits=%d, misses=%d, hitRatio=%.2f%%, puts=%d, evictions=%d}",
                               getHits(), getMisses(), getHitRatio() * 100, getPuts(), getEvictions());
        }
    }

    /**
     * 缓存状态信息
     */
    public static class CacheStatus {
        private long entryCount;
        private long memoryUsage;
        private long maxMemory;
        private double memoryUsageRatio;
        private double hitRatio;

        // Getters and Setters
        public long getEntryCount() { return entryCount; }
        public void setEntryCount(long entryCount) { this.entryCount = entryCount; }

        public long getMemoryUsage() { return memoryUsage; }
        public void setMemoryUsage(long memoryUsage) { this.memoryUsage = memoryUsage; }

        public long getMaxMemory() { return maxMemory; }
        public void setMaxMemory(long maxMemory) { this.maxMemory = maxMemory; }

        public double getMemoryUsageRatio() { return memoryUsageRatio; }
        public void setMemoryUsageRatio(double memoryUsageRatio) { this.memoryUsageRatio = memoryUsageRatio; }

        public double getHitRatio() { return hitRatio; }
        public void setHitRatio(double hitRatio) { this.hitRatio = hitRatio; }

        @Override
        public String toString() {
            return String.format("CacheStatus{entries=%d, memory=%dMB/%.2f%%, hitRatio=%.2f%%}",
                               entryCount, memoryUsage / 1024 / 1024, memoryUsageRatio * 100, hitRatio * 100);
        }
    }

    /**
     * 缓存配置
     */
    public static class CacheConfig {
        private long maxEntries = 100000;                    // 最大条目数
        private long maxMemoryBytes = 512 * 1024 * 1024;     // 最大内存使用量 512MB
        private long defaultTTLMs = 3600000;                 // 默认过期时间 1小时
        private long cleanupIntervalMs = 60000;              // 清理间隔 1分钟
        private long memoryCheckIntervalMs = 30000;          // 内存检查间隔 30秒

        // Getters and Setters
        public long getMaxEntries() { return maxEntries; }
        public void setMaxEntries(long maxEntries) { this.maxEntries = maxEntries; }

        public long getMaxMemoryBytes() { return maxMemoryBytes; }
        public void setMaxMemoryBytes(long maxMemoryBytes) { this.maxMemoryBytes = maxMemoryBytes; }

        public long getDefaultTTLMs() { return defaultTTLMs; }
        public void setDefaultTTLMs(long defaultTTLMs) { this.defaultTTLMs = defaultTTLMs; }

        public long getCleanupIntervalMs() { return cleanupIntervalMs; }
        public void setCleanupIntervalMs(long cleanupIntervalMs) { this.cleanupIntervalMs = cleanupIntervalMs; }

        public long getMemoryCheckIntervalMs() { return memoryCheckIntervalMs; }
        public void setMemoryCheckIntervalMs(long memoryCheckIntervalMs) { this.memoryCheckIntervalMs = memoryCheckIntervalMs; }
    }
}