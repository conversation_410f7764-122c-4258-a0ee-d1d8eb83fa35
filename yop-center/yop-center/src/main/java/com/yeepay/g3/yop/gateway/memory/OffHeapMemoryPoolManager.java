/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * title: 堆外内存池化管理器<br/>
 * description: 高性能堆外内存池管理，支持多种内存块大小和线程本地缓存<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class OffHeapMemoryPoolManager {

    private static final Logger log = Logger.getLogger(OffHeapMemoryPoolManager.class.getName());
    private static final OffHeapMemoryPoolManager INSTANCE = new OffHeapMemoryPoolManager();

    // 预定义的内存块大小（字节）
    private static final int[] CHUNK_SIZES = {
        512,        // 0.5KB - 小消息
        1024,       // 1KB - 小消息
        2048,       // 2KB - 小消息
        4096,       // 4KB - 普通消息
        8192,       // 8KB - 普通消息
        16384,      // 16KB - 中等消息
        32768,      // 32KB - 中等消息
        65536,      // 64KB - 大消息
        131072,     // 128KB - 大消息
        262144,     // 256KB - 超大消息
        524288,     // 512KB - 文件上传
        1048576     // 1MB - 大文件处理
    };

    // 线程本地内存缓存
    private final ThreadLocal<ThreadLocalMemoryCache> threadLocalCache = 
        ThreadLocal.withInitial(ThreadLocalMemoryCache::new);

    // 内存使用统计
    private final MemoryPoolStats stats = new MemoryPoolStats();
    
    // 内存池配置
    private final MemoryPoolConfig config;

    // 全局内存池，按大小分类
    private final ConcurrentHashMap<Integer, MemoryChunkPool> globalPools = new ConcurrentHashMap<>();

    private OffHeapMemoryPoolManager() {
        this.config = new MemoryPoolConfig();
        initializeGlobalPools();
        
        log.info("OffHeapMemoryPoolManager initialized with config: " + config);
        startMemoryMonitoring();
    }

    public static OffHeapMemoryPoolManager getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化全局内存池
     */
    private void initializeGlobalPools() {
        for (int size : CHUNK_SIZES) {
            globalPools.put(size, new MemoryChunkPool(size, config.getPoolSize()));
        }
    }

    /**
     * 分配堆外内存
     */
    public ByteBuffer allocateDirectBuffer(int capacity) {
        // 记录分配统计
        stats.recordAllocation(capacity);
        
        try {
            // 优先从线程本地缓存分配
            ThreadLocalMemoryCache cache = threadLocalCache.get();
            ByteBuffer cached = cache.allocate(capacity);
            if (cached != null) {
                stats.recordCacheHit();
                return cached;
            }
            
            // 从全局池分配
            int optimalSize = findOptimalChunkSize(capacity);
            MemoryChunkPool pool = globalPools.get(optimalSize);
            if (pool != null) {
                ByteBuffer pooled = pool.allocate();
                if (pooled != null) {
                    stats.recordPoolHit();
                    // 限制容量到请求大小
                    pooled.limit(capacity);
                    return pooled;
                }
            }
            
            // 直接分配新的堆外内存
            ByteBuffer buffer = ByteBuffer.allocateDirect(capacity);
            stats.recordDirectAllocation();
            
            return buffer;
        } catch (OutOfMemoryError e) {
            stats.recordOutOfMemory();
            log.log(Level.SEVERE, "Failed to allocate direct buffer: capacity=" + capacity, e);
            
            // 尝试触发GC和清理缓存
            System.gc();
            clearThreadLocalCaches();
            
            // 重试一次
            return ByteBuffer.allocateDirect(capacity);
        }
    }

    /**
     * 分配最佳大小的内存块
     */
    public ByteBuffer allocateOptimalBuffer(int requestedSize) {
        int optimalSize = findOptimalChunkSize(requestedSize);
        ByteBuffer buffer = allocateDirectBuffer(optimalSize);
        buffer.limit(requestedSize);
        return buffer;
    }

    /**
     * 查找最佳内存块大小
     */
    private int findOptimalChunkSize(int requestedSize) {
        for (int size : CHUNK_SIZES) {
            if (size >= requestedSize) {
                return size;
            }
        }
        // 如果超过预定义大小，返回下一个2的幂次方
        return Integer.highestOneBit(requestedSize - 1) << 1;
    }

    /**
     * 释放缓存的ByteBuffer到线程本地缓存或全局池
     */
    public void releaseToCacheIfPossible(ByteBuffer buffer) {
        if (buffer == null || !buffer.isDirect()) {
            return;
        }

        int capacity = buffer.capacity();
        
        // 重置buffer状态
        buffer.clear();
        
        // 尝试放入线程本地缓存
        ThreadLocalMemoryCache cache = threadLocalCache.get();
        if (cache.cache(buffer)) {
            stats.recordCacheStore();
            return;
        }
        
        // 尝试放入全局池
        if (isOptimalSize(capacity)) {
            MemoryChunkPool pool = globalPools.get(capacity);
            if (pool != null && pool.release(buffer)) {
                stats.recordPoolStore();
                return;
            }
        }
        
        // 无法缓存，让GC处理
        stats.recordDirectRelease();
    }

    /**
     * 检查是否为最佳大小
     */
    private boolean isOptimalSize(int capacity) {
        for (int size : CHUNK_SIZES) {
            if (size == capacity) {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建内存视图数组（零拷贝）
     */
    public ByteBuffer[] createCompositeBuffer(ByteBuffer... buffers) {
        stats.recordCompositeCreation();
        return buffers.clone();
    }

    /**
     * 清理线程本地缓存
     */
    public void clearThreadLocalCaches() {
        ThreadLocalMemoryCache cache = threadLocalCache.get();
        if (cache != null) {
            cache.clear();
        }
        stats.recordCacheClear();
    }

    /**
     * 获取内存使用统计
     */
    public MemoryPoolStats getStats() {
        return stats;
    }

    /**
     * 启动内存监控
     */
    private void startMemoryMonitoring() {
        // 定期清理过期的线程本地缓存
        Thread cleanupThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(config.getCleanupIntervalMs());
                    performCleanup();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, "memory-pool-cleanup");
        
        cleanupThread.setDaemon(true);
        cleanupThread.start();
    }

    /**
     * 执行清理工作
     */
    private void performCleanup() {
        try {
            // 清理过期的线程本地缓存
            clearExpiredCaches();
            
            // 清理全局池中的过期buffer
            cleanupGlobalPools();
            
            // 记录内存使用情况
            logMemoryUsage();
            
            // 检查内存泄露
            checkMemoryLeaks();
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error during memory pool cleanup", e);
        }
    }

    /**
     * 清理过期缓存
     */
    private void clearExpiredCaches() {
        // 这里可以实现更复杂的过期策略
        // 目前简单地定期清理
    }

    /**
     * 清理全局池
     */
    private void cleanupGlobalPools() {
        for (MemoryChunkPool pool : globalPools.values()) {
            pool.cleanup();
        }
    }

    /**
     * 记录内存使用情况
     */
    private void logMemoryUsage() {
        if (log.isLoggable(Level.FINE)) {
            long totalAllocations = stats.getTotalAllocations();
            double cacheHitRatio = stats.getCacheHitRatio();
            
            log.fine("Memory Pool Stats - Total Allocations: " + totalAllocations + 
                    ", Cache Hit Ratio: " + String.format("%.2f%%", cacheHitRatio * 100));
        }
    }

    /**
     * 检查内存泄露
     */
    private void checkMemoryLeaks() {
        long outOfMemoryCount = stats.getOutOfMemoryCount();
        if (outOfMemoryCount > 0) {
            log.warning("Detected " + outOfMemoryCount + " OutOfMemory events, possible memory leak");
        }
    }

    /**
     * 优雅关闭
     */
    public void shutdown() {
        log.info("Shutting down OffHeapMemoryPoolManager");
        clearThreadLocalCaches();
        
        // 清理全局池
        for (MemoryChunkPool pool : globalPools.values()) {
            pool.shutdown();
        }
        globalPools.clear();
    }

    /**
     * 线程本地内存缓存
     */
    private static class ThreadLocalMemoryCache {
        private final ConcurrentHashMap<Integer, ByteBuffer> cache = new ConcurrentHashMap<>();
        private final int maxCacheSize = 16; // 每个线程最多缓存16个不同大小的buffer
        
        ByteBuffer allocate(int capacity) {
            int optimalSize = findOptimalSize(capacity);
            ByteBuffer buffer = cache.remove(optimalSize);
            if (buffer != null) {
                buffer.clear();
                buffer.limit(capacity);
            }
            return buffer;
        }
        
        boolean cache(ByteBuffer buffer) {
            if (cache.size() >= maxCacheSize) {
                return false;
            }
            
            int capacity = buffer.capacity();
            cache.put(capacity, buffer);
            
            return true;
        }
        
        void clear() {
            cache.clear();
        }
        
        private int findOptimalSize(int requestedSize) {
            for (int size : CHUNK_SIZES) {
                if (size >= requestedSize) {
                    return size;
                }
            }
            return Integer.highestOneBit(requestedSize - 1) << 1;
        }
    }

    /**
     * 内存块池
     */
    private static class MemoryChunkPool {
        private final int chunkSize;
        private final int maxPoolSize;
        private final ConcurrentHashMap<Integer, ByteBuffer> pool = new ConcurrentHashMap<>();
        private final AtomicLong allocateCount = new AtomicLong();
        private final AtomicLong releaseCount = new AtomicLong();
        
        public MemoryChunkPool(int chunkSize, int maxPoolSize) {
            this.chunkSize = chunkSize;
            this.maxPoolSize = maxPoolSize;
        }
        
        public ByteBuffer allocate() {
            // 尝试从池中获取
            for (Integer key : pool.keySet()) {
                ByteBuffer buffer = pool.remove(key);
                if (buffer != null) {
                    allocateCount.incrementAndGet();
                    buffer.clear();
                    return buffer;
                }
            }
            
            // 池中没有，分配新的
            ByteBuffer buffer = ByteBuffer.allocateDirect(chunkSize);
            allocateCount.incrementAndGet();
            return buffer;
        }
        
        public boolean release(ByteBuffer buffer) {
            if (pool.size() >= maxPoolSize) {
                return false;
            }
            
            if (buffer.capacity() == chunkSize) {
                buffer.clear();
                pool.put(buffer.hashCode(), buffer);
                releaseCount.incrementAndGet();
                return true;
            }
            
            return false;
        }
        
        public void cleanup() {
            // 清理一半的缓存buffer
            if (pool.size() > maxPoolSize / 2) {
                int toRemove = pool.size() / 2;
                int removed = 0;
                for (Integer key : pool.keySet()) {
                    if (removed >= toRemove) {
                        break;
                    }
                    pool.remove(key);
                    removed++;
                }
            }
        }
        
        public void shutdown() {
            pool.clear();
        }
        
        public long getAllocateCount() {
            return allocateCount.get();
        }
        
        public long getReleaseCount() {
            return releaseCount.get();
        }
        
        public int getPoolSize() {
            return pool.size();
        }
    }

    /**
     * 内存池统计信息
     */
    public static class MemoryPoolStats {
        private final LongAdder totalAllocations = new LongAdder();
        private final LongAdder cacheHits = new LongAdder();
        private final LongAdder cacheMisses = new LongAdder();
        private final LongAdder poolHits = new LongAdder();
        private final LongAdder poolMisses = new LongAdder();
        private final LongAdder directAllocations = new LongAdder();
        private final LongAdder cacheStores = new LongAdder();
        private final LongAdder poolStores = new LongAdder();
        private final LongAdder directReleases = new LongAdder();
        private final LongAdder cacheClearCount = new LongAdder();
        private final LongAdder compositeCreations = new LongAdder();
        private final AtomicLong outOfMemoryCount = new AtomicLong();

        public void recordAllocation(int size) {
            totalAllocations.increment();
        }

        public void recordCacheHit() {
            cacheHits.increment();
        }

        public void recordCacheMiss() {
            cacheMisses.increment();
        }

        public void recordPoolHit() {
            poolHits.increment();
        }

        public void recordPoolMiss() {
            poolMisses.increment();
        }

        public void recordDirectAllocation() {
            directAllocations.increment();
        }

        public void recordCacheStore() {
            cacheStores.increment();
        }

        public void recordPoolStore() {
            poolStores.increment();
        }

        public void recordDirectRelease() {
            directReleases.increment();
        }

        public void recordCacheClear() {
            cacheClearCount.increment();
        }

        public void recordCompositeCreation() {
            compositeCreations.increment();
        }

        public void recordOutOfMemory() {
            outOfMemoryCount.incrementAndGet();
        }

        public long getTotalAllocations() {
            return totalAllocations.sum();
        }

        public double getCacheHitRatio() {
            long hits = cacheHits.sum();
            long misses = cacheMisses.sum();
            long total = hits + misses;
            return total > 0 ? (double) hits / total : 0.0;
        }

        public double getPoolHitRatio() {
            long hits = poolHits.sum();
            long misses = poolMisses.sum();
            long total = hits + misses;
            return total > 0 ? (double) hits / total : 0.0;
        }

        public long getCacheHits() {
            return cacheHits.sum();
        }

        public long getPoolHits() {
            return poolHits.sum();
        }

        public long getDirectAllocations() {
            return directAllocations.sum();
        }

        public long getCacheStores() {
            return cacheStores.sum();
        }

        public long getPoolStores() {
            return poolStores.sum();
        }

        public long getDirectReleases() {
            return directReleases.sum();
        }

        public long getCacheClearCount() {
            return cacheClearCount.sum();
        }

        public long getCompositeCreations() {
            return compositeCreations.sum();
        }

        public long getOutOfMemoryCount() {
            return outOfMemoryCount.get();
        }
    }

    /**
     * 内存池配置
     */
    public static class MemoryPoolConfig {
        private int poolSize = 64;  // 每个大小的池最多缓存64个buffer
        private long cleanupIntervalMs = 30000; // 30秒清理一次

        public int getPoolSize() { 
            return poolSize; 
        }
        
        public void setPoolSize(int poolSize) { 
            this.poolSize = poolSize; 
        }

        public long getCleanupIntervalMs() { 
            return cleanupIntervalMs; 
        }
        
        public void setCleanupIntervalMs(long cleanupIntervalMs) { 
            this.cleanupIntervalMs = cleanupIntervalMs; 
        }

        @Override
        public String toString() {
            return String.format("MemoryPoolConfig{poolSize=%d, cleanupInterval=%dms}",
                               poolSize, cleanupIntervalMs);
        }
    }
}