/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * title: 内存监控诊断服务<br/>
 * description: 提供详细的内存使用监控、泄漏检测和性能分析<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class MemoryMonitoringService {

    private static final Logger log = Logger.getLogger(MemoryMonitoringService.class.getName());
    private static final MemoryMonitoringService INSTANCE = new MemoryMonitoringService();

    // 组件引用
    private final OffHeapMemoryPoolManager memoryPool;
    private final ObjectPoolManager objectPool;
    private final OffHeapCacheManager cacheManager;
    private final ZeroCopyDataTransfer dataTransfer;
    private final GCOptimizationConfig gcConfig;

    // 监控数据
    private final MemoryMetrics metrics = new MemoryMetrics();
    private final Map<String, ComponentStats> componentStats = new ConcurrentHashMap<>();
    
    // 调度器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3, 
        r -> {
            Thread t = new Thread(r, "memory-monitor");
            t.setDaemon(true);
            return t;
        });

    // 配置
    private final MonitoringConfig config = new MonitoringConfig();
    
    // 告警状态
    private final AtomicLong lastMemoryWarning = new AtomicLong(0);
    private final AtomicLong lastLeakWarning = new AtomicLong(0);

    private MemoryMonitoringService() {
        this.memoryPool = OffHeapMemoryPoolManager.getInstance();
        this.objectPool = ObjectPoolManager.getInstance();
        this.cacheManager = OffHeapCacheManager.getInstance();
        this.dataTransfer = ZeroCopyDataTransfer.getInstance();
        this.gcConfig = GCOptimizationConfig.getInstance();
        
        log.info("MemoryMonitoringService initialized");
        startMonitoring();
    }

    public static MemoryMonitoringService getInstance() {
        return INSTANCE;
    }

    /**
     * 启动监控
     */
    private void startMonitoring() {
        // 基础内存监控 - 每10秒
        scheduler.scheduleAtFixedRate(this::collectBasicMetrics, 
                                    10, 10, TimeUnit.SECONDS);
        
        // 详细分析 - 每1分钟
        scheduler.scheduleAtFixedRate(this::performDetailedAnalysis, 
                                    60, 60, TimeUnit.SECONDS);
        
        // 内存泄漏检测 - 每5分钟
        scheduler.scheduleAtFixedRate(this::detectMemoryLeaks, 
                                    300, 300, TimeUnit.SECONDS);
        
        // 性能报告 - 每10分钟
        scheduler.scheduleAtFixedRate(this::generatePerformanceReport, 
                                    600, 600, TimeUnit.SECONDS);
    }

    /**
     * 收集基础指标
     */
    private void collectBasicMetrics() {
        try {
            // JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            metrics.recordJVMMemory(usedMemory, totalMemory, maxMemory);
            
            // 组件统计
            collectComponentStats();
            
            // 检查内存使用阈值
            checkMemoryThresholds(usedMemory, maxMemory);
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error collecting basic metrics", e);
        }
    }

    /**
     * 收集组件统计信息
     */
    private void collectComponentStats() {
        // 内存池统计
        OffHeapMemoryPoolManager.MemoryPoolStats poolStats = memoryPool.getStats();
        ComponentStats memPoolStats = new ComponentStats("MemoryPool");
        memPoolStats.setAllocations(poolStats.getTotalAllocations());
        memPoolStats.setCacheHitRatio(poolStats.getCacheHitRatio());
        memPoolStats.setMemoryUsage(poolStats.getTotalAllocations() * 1024); // 估算内存使用
        componentStats.put("MemoryPool", memPoolStats);

        // 对象池统计
        ObjectPoolManager.PoolStats objPoolStats = objectPool.getGlobalStats();
        ComponentStats objectStats = new ComponentStats("ObjectPool");
        objectStats.setHitRatio(objPoolStats.getHitRatio());
        objectStats.setBorrows(objPoolStats.getBorrows());
        objectStats.setReturns(objPoolStats.getReturns());
        componentStats.put("ObjectPool", objectStats);

        // 缓存统计
        OffHeapCacheManager.CacheStats cacheStats = cacheManager.getStats();
        ComponentStats cacheStatsObj = new ComponentStats("Cache");
        cacheStatsObj.setHitRatio(cacheStats.getHitRatio());
        cacheStatsObj.setHits(cacheStats.getHits());
        cacheStatsObj.setMisses(cacheStats.getMisses());
        componentStats.put("Cache", cacheStatsObj);

        // 零拷贝统计
        ZeroCopyDataTransfer.TransferStats transferStats = dataTransfer.getStats();
        ComponentStats transferStatsObj = new ComponentStats("ZeroCopy");
        transferStatsObj.setTransfers(transferStats.getFileTransferCount());
        transferStatsObj.setTransferredBytes(transferStats.getFileTransferBytes());
        transferStatsObj.setThroughput(transferStats.getFileTransferThroughputMBps());
        componentStats.put("ZeroCopy", transferStatsObj);
    }

    /**
     * 检查内存阈值
     */
    private void checkMemoryThresholds(long usedMemory, long maxMemory) {
        double usageRatio = (double) usedMemory / maxMemory;
        
        if (usageRatio > config.getMemoryWarningThreshold()) {
            long now = System.currentTimeMillis();
            if (now - lastMemoryWarning.get() > config.getWarningCooldownMs()) {
                log.warning(String.format("High memory usage detected: %.2f%% (%dMB/%dMB)", 
                           usageRatio * 100, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024));
                lastMemoryWarning.set(now);
                metrics.recordMemoryWarning();
            }
        }

        if (usageRatio > config.getMemoryCriticalThreshold()) {
            log.severe(String.format("Critical memory usage: %.2f%% - Consider immediate action", 
                      usageRatio * 100));
            metrics.recordMemoryCritical();
            
            // 触发紧急清理
            triggerEmergencyCleanup();
        }
    }

    /**
     * 详细分析
     */
    private void performDetailedAnalysis() {
        try {
            // 分析内存增长趋势
            analyzeMemoryTrend();
            
            // 分析组件性能
            analyzeComponentPerformance();
            
            // 检查资源利用效率
            checkResourceEfficiency();
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error during detailed analysis", e);
        }
    }

    /**
     * 分析内存增长趋势
     */
    private void analyzeMemoryTrend() {
        List<Long> recentUsage = metrics.getRecentMemoryUsage(10); // 最近10次记录
        if (recentUsage.size() < 3) {
            return;
        }

        // 计算增长率
        long first = recentUsage.get(0);
        long last = recentUsage.get(recentUsage.size() - 1);
        double growthRate = (double) (last - first) / first;

        if (growthRate > config.getMemoryGrowthWarningThreshold()) {
            log.warning(String.format("High memory growth rate detected: %.2f%% over last %d samples", 
                       growthRate * 100, recentUsage.size()));
            metrics.recordGrowthWarning();
        }
    }

    /**
     * 分析组件性能
     */
    private void analyzeComponentPerformance() {
        for (Map.Entry<String, ComponentStats> entry : componentStats.entrySet()) {
            String component = entry.getKey();
            ComponentStats stats = entry.getValue();
            
            // 检查命中率
            if (stats.getHitRatio() < config.getMinHitRatio() && stats.getRequests() > 100) {
                log.warning(String.format("Low hit ratio for %s: %.2f%%", 
                           component, stats.getHitRatio() * 100));
            }
            
            // 检查错误率
            if (stats.getErrorRate() > config.getMaxErrorRate()) {
                log.warning(String.format("High error rate for %s: %.2f%%", 
                           component, stats.getErrorRate() * 100));
            }
        }
    }

    /**
     * 检查资源利用效率
     */
    private void checkResourceEfficiency() {
        // 检查内存池效率
        OffHeapMemoryPoolManager.MemoryPoolStats poolStats = memoryPool.getStats();
        if (poolStats.getCacheHitRatio() < 0.8 && poolStats.getTotalAllocations() > 1000) {
            log.info("Memory pool cache hit ratio could be improved: " + 
                    String.format("%.2f%%", poolStats.getCacheHitRatio() * 100));
        }

        // 检查缓存效率
        OffHeapCacheManager.CacheStatus cacheStatus = cacheManager.getStatus();
        if (cacheStatus.getMemoryUsageRatio() > 0.9) {
            log.info("Cache memory usage is high: " + 
                    String.format("%.2f%%", cacheStatus.getMemoryUsageRatio() * 100));
        }
    }

    /**
     * 检测内存泄漏
     */
    private void detectMemoryLeaks() {
        try {
            // 检查长期内存增长趋势
            List<Long> longTermUsage = metrics.getRecentMemoryUsage(50); // 最近50次记录
            if (longTermUsage.size() < 10) {
                return;
            }

            // 计算长期增长趋势
            long firstQuarter = longTermUsage.subList(0, longTermUsage.size() / 4)
                               .stream().mapToLong(Long::longValue).sum() / (longTermUsage.size() / 4);
            long lastQuarter = longTermUsage.subList(3 * longTermUsage.size() / 4, longTermUsage.size())
                              .stream().mapToLong(Long::longValue).sum() / (longTermUsage.size() / 4);

            double longTermGrowth = (double) (lastQuarter - firstQuarter) / firstQuarter;

            if (longTermGrowth > config.getLeakDetectionThreshold()) {
                long now = System.currentTimeMillis();
                if (now - lastLeakWarning.get() > config.getLeakWarningCooldownMs()) {
                    log.severe(String.format("Potential memory leak detected! Long-term growth: %.2f%%", 
                              longTermGrowth * 100));
                    lastLeakWarning.set(now);
                    metrics.recordLeakSuspicion();
                    
                    // 触发详细诊断
                    performLeakDiagnosis();
                }
            }

        } catch (Exception e) {
            log.log(Level.WARNING, "Error during memory leak detection", e);
        }
    }

    /**
     * 执行泄漏诊断
     */
    private void performLeakDiagnosis() {
        StringBuilder diagnosis = new StringBuilder("Memory Leak Diagnosis:\n");
        
        // 分析各组件的内存使用
        diagnosis.append("Component Memory Usage:\n");
        componentStats.forEach((name, stats) -> {
            diagnosis.append(String.format("  %s: %s\n", name, stats.toString()));
        });
        
        // 分析GC情况
        GCOptimizationConfig.GCMetrics gcMetrics = gcConfig.getMetrics();
        diagnosis.append(String.format("GC Stats: Count=%d, AvgTime=%.2fms, Frequency=%.2f/s\n",
                        gcMetrics.getGCCount(), gcMetrics.getAverageGCTime(), gcMetrics.getGCFrequency()));
        
        // 提供建议
        diagnosis.append("Recommendations:\n");
        diagnosis.append("  1. Check for unclosed resources\n");
        diagnosis.append("  2. Review caching strategies\n");
        diagnosis.append("  3. Analyze object lifecycle\n");
        diagnosis.append("  4. Consider heap dump analysis\n");
        
        log.severe(diagnosis.toString());
    }

    /**
     * 生成性能报告
     */
    private void generatePerformanceReport() {
        try {
            StringBuilder report = new StringBuilder("\n=== Memory Performance Report ===\n");
            
            // JVM内存统计
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            
            report.append("JVM Memory:\n");
            report.append(String.format("  Used: %dMB / %dMB (%.2f%%)\n", 
                         usedMemory / 1024 / 1024, maxMemory / 1024 / 1024,
                         (double) usedMemory / maxMemory * 100));
            
            // 组件性能统计
            report.append("Component Performance:\n");
            componentStats.forEach((name, stats) -> {
                report.append(String.format("  %s: %s\n", name, stats.getSummary()));
            });
            
            // 整体性能指标
            report.append("Performance Metrics:\n");
            report.append(String.format("  Memory Warnings: %d\n", metrics.getMemoryWarnings()));
            report.append(String.format("  Leak Suspicions: %d\n", metrics.getLeakSuspicions()));
            report.append(String.format("  Growth Warnings: %d\n", metrics.getGrowthWarnings()));
            
            // 性能评估
            report.append("Performance Assessment:\n");
            double avgHitRatio = componentStats.values().stream()
                               .mapToDouble(ComponentStats::getHitRatio)
                               .average().orElse(0.0);
            report.append(String.format("  Average Hit Ratio: %.2f%%\n", avgHitRatio * 100));
            report.append(String.format("  Memory Efficiency: %s\n", 
                         (double) usedMemory / maxMemory < 0.8 ? "✓ GOOD" : "⚠ HIGH"));
            
            report.append("===============================\n");
            
            log.info(report.toString());
            
        } catch (Exception e) {
            log.log(Level.WARNING, "Error generating performance report", e);
        }
    }

    /**
     * 触发紧急清理
     */
    private void triggerEmergencyCleanup() {
        log.warning("Triggering emergency memory cleanup");
        
        try {
            // 清理对象池
            objectPool.clearAllPools();
            
            // 部分清理缓存
            // 注意：不完全清理缓存，避免影响性能
            
            // 清理内存池中的线程本地缓存
            memoryPool.clearThreadLocalCaches();
            
            // 建议JVM执行GC
            System.gc();
            
            log.info("Emergency cleanup completed");
            
        } catch (Exception e) {
            log.log(Level.SEVERE, "Error during emergency cleanup", e);
        }
    }

    /**
     * 获取当前内存状态
     */
    public MemoryStatus getCurrentStatus() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        MemoryStatus status = new MemoryStatus();
        status.setJvmUsedMemory(usedMemory);
        status.setJvmMaxMemory(maxMemory);
        status.setJvmUsageRatio((double) usedMemory / maxMemory);
        
        // 组件状态
        status.setMemoryPoolStats(memoryPool.getStats());
        status.setObjectPoolStats(objectPool.getGlobalStats());
        status.setCacheStatus(cacheManager.getStatus());
        status.setZeroCopyStats(dataTransfer.getStats());
        
        return status;
    }

    /**
     * 获取监控指标
     */
    public MemoryMetrics getMetrics() {
        return metrics;
    }

    /**
     * 优雅关闭
     */
    public void shutdown() {
        log.info("Shutting down MemoryMonitoringService");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 内存监控指标
     */
    public static class MemoryMetrics {
        private final List<Long> memoryUsageHistory = new ArrayList<>();
        private final LongAdder memoryWarnings = new LongAdder();
        private final LongAdder memoryCriticals = new LongAdder();
        private final LongAdder growthWarnings = new LongAdder();
        private final LongAdder leakSuspicions = new LongAdder();
        private final AtomicLong lastJVMUsed = new AtomicLong(0);
        private final AtomicLong lastJVMMax = new AtomicLong(0);

        public synchronized void recordJVMMemory(long used, long total, long max) {
            lastJVMUsed.set(used);
            lastJVMMax.set(max);
            
            memoryUsageHistory.add(used);
            if (memoryUsageHistory.size() > 100) {
                memoryUsageHistory.remove(0);
            }
        }

        public void recordMemoryWarning() { memoryWarnings.increment(); }
        public void recordMemoryCritical() { memoryCriticals.increment(); }
        public void recordGrowthWarning() { growthWarnings.increment(); }
        public void recordLeakSuspicion() { leakSuspicions.increment(); }

        public synchronized List<Long> getRecentMemoryUsage(int count) {
            int size = memoryUsageHistory.size();
            int start = Math.max(0, size - count);
            return new ArrayList<>(memoryUsageHistory.subList(start, size));
        }

        public long getMemoryWarnings() { return memoryWarnings.sum(); }
        public long getMemoryCriticals() { return memoryCriticals.sum(); }
        public long getGrowthWarnings() { return growthWarnings.sum(); }
        public long getLeakSuspicions() { return leakSuspicions.sum(); }
        public long getCurrentJVMUsed() { return lastJVMUsed.get(); }
        public long getCurrentJVMMax() { return lastJVMMax.get(); }
    }

    /**
     * 组件统计信息
     */
    public static class ComponentStats {
        private final String name;
        private double hitRatio = 0.0;
        private long hits = 0;
        private long misses = 0;
        private long allocations = 0;
        private long borrows = 0;
        private long returns = 0;
        private long transfers = 0;
        private long transferredBytes = 0;
        private double throughput = 0.0;
        private long memoryUsage = 0;
        private double cacheHitRatio = 0.0;
        private long errors = 0;
        private long requests = 0;

        public ComponentStats(String name) {
            this.name = name;
        }

        // Getters and Setters
        public String getName() { return name; }
        public double getHitRatio() { return hitRatio; }
        public void setHitRatio(double hitRatio) { this.hitRatio = hitRatio; }
        public long getHits() { return hits; }
        public void setHits(long hits) { this.hits = hits; }
        public long getMisses() { return misses; }
        public void setMisses(long misses) { this.misses = misses; }
        public long getAllocations() { return allocations; }
        public void setAllocations(long allocations) { this.allocations = allocations; }
        public long getBorrows() { return borrows; }
        public void setBorrows(long borrows) { this.borrows = borrows; }
        public long getReturns() { return returns; }
        public void setReturns(long returns) { this.returns = returns; }
        public long getTransfers() { return transfers; }
        public void setTransfers(long transfers) { this.transfers = transfers; }
        public long getTransferredBytes() { return transferredBytes; }
        public void setTransferredBytes(long transferredBytes) { this.transferredBytes = transferredBytes; }
        public double getThroughput() { return throughput; }
        public void setThroughput(double throughput) { this.throughput = throughput; }
        public long getMemoryUsage() { return memoryUsage; }
        public void setMemoryUsage(long memoryUsage) { this.memoryUsage = memoryUsage; }
        public double getCacheHitRatio() { return cacheHitRatio; }
        public void setCacheHitRatio(double cacheHitRatio) { this.cacheHitRatio = cacheHitRatio; }
        public long getErrors() { return errors; }
        public void setErrors(long errors) { this.errors = errors; }
        public long getRequests() { return requests; }
        public void setRequests(long requests) { this.requests = requests; }

        public double getErrorRate() {
            return requests > 0 ? (double) errors / requests : 0.0;
        }

        public String getSummary() {
            return String.format("HitRatio=%.2f%%, Requests=%d, Errors=%d", 
                               hitRatio * 100, requests, errors);
        }

        @Override
        public String toString() {
            return String.format("%s{hitRatio=%.2f%%, requests=%d, errors=%d, memory=%dMB}",
                               name, hitRatio * 100, requests, errors, memoryUsage / 1024 / 1024);
        }
    }

    /**
     * 内存状态信息
     */
    public static class MemoryStatus {
        private long jvmUsedMemory;
        private long jvmMaxMemory;
        private double jvmUsageRatio;
        private OffHeapMemoryPoolManager.MemoryPoolStats memoryPoolStats;
        private ObjectPoolManager.PoolStats objectPoolStats;
        private OffHeapCacheManager.CacheStatus cacheStatus;
        private ZeroCopyDataTransfer.TransferStats zeroCopyStats;

        // Getters and Setters
        public long getJvmUsedMemory() { return jvmUsedMemory; }
        public void setJvmUsedMemory(long jvmUsedMemory) { this.jvmUsedMemory = jvmUsedMemory; }

        public long getJvmMaxMemory() { return jvmMaxMemory; }
        public void setJvmMaxMemory(long jvmMaxMemory) { this.jvmMaxMemory = jvmMaxMemory; }

        public double getJvmUsageRatio() { return jvmUsageRatio; }
        public void setJvmUsageRatio(double jvmUsageRatio) { this.jvmUsageRatio = jvmUsageRatio; }

        public OffHeapMemoryPoolManager.MemoryPoolStats getMemoryPoolStats() { return memoryPoolStats; }
        public void setMemoryPoolStats(OffHeapMemoryPoolManager.MemoryPoolStats memoryPoolStats) { this.memoryPoolStats = memoryPoolStats; }

        public ObjectPoolManager.PoolStats getObjectPoolStats() { return objectPoolStats; }
        public void setObjectPoolStats(ObjectPoolManager.PoolStats objectPoolStats) { this.objectPoolStats = objectPoolStats; }

        public OffHeapCacheManager.CacheStatus getCacheStatus() { return cacheStatus; }
        public void setCacheStatus(OffHeapCacheManager.CacheStatus cacheStatus) { this.cacheStatus = cacheStatus; }

        public ZeroCopyDataTransfer.TransferStats getZeroCopyStats() { return zeroCopyStats; }
        public void setZeroCopyStats(ZeroCopyDataTransfer.TransferStats zeroCopyStats) { this.zeroCopyStats = zeroCopyStats; }
    }

    /**
     * 监控配置
     */
    public static class MonitoringConfig {
        private double memoryWarningThreshold = 0.8;      // 80%内存使用率告警
        private double memoryCriticalThreshold = 0.95;    // 95%内存使用率紧急
        private double memoryGrowthWarningThreshold = 0.2; // 20%增长率告警
        private double leakDetectionThreshold = 0.1;      // 10%长期增长视为泄漏
        private double minHitRatio = 0.8;                 // 最小命中率80%
        private double maxErrorRate = 0.01;               // 最大错误率1%
        private long warningCooldownMs = 300000;          // 告警冷却时间5分钟
        private long leakWarningCooldownMs = 1800000;     // 泄漏告警冷却30分钟

        // Getters and Setters
        public double getMemoryWarningThreshold() { return memoryWarningThreshold; }
        public void setMemoryWarningThreshold(double memoryWarningThreshold) { this.memoryWarningThreshold = memoryWarningThreshold; }

        public double getMemoryCriticalThreshold() { return memoryCriticalThreshold; }
        public void setMemoryCriticalThreshold(double memoryCriticalThreshold) { this.memoryCriticalThreshold = memoryCriticalThreshold; }

        public double getMemoryGrowthWarningThreshold() { return memoryGrowthWarningThreshold; }
        public void setMemoryGrowthWarningThreshold(double memoryGrowthWarningThreshold) { this.memoryGrowthWarningThreshold = memoryGrowthWarningThreshold; }

        public double getLeakDetectionThreshold() { return leakDetectionThreshold; }
        public void setLeakDetectionThreshold(double leakDetectionThreshold) { this.leakDetectionThreshold = leakDetectionThreshold; }

        public double getMinHitRatio() { return minHitRatio; }
        public void setMinHitRatio(double minHitRatio) { this.minHitRatio = minHitRatio; }

        public double getMaxErrorRate() { return maxErrorRate; }
        public void setMaxErrorRate(double maxErrorRate) { this.maxErrorRate = maxErrorRate; }

        public long getWarningCooldownMs() { return warningCooldownMs; }
        public void setWarningCooldownMs(long warningCooldownMs) { this.warningCooldownMs = warningCooldownMs; }

        public long getLeakWarningCooldownMs() { return leakWarningCooldownMs; }
        public void setLeakWarningCooldownMs(long leakWarningCooldownMs) { this.leakWarningCooldownMs = leakWarningCooldownMs; }
    }
}