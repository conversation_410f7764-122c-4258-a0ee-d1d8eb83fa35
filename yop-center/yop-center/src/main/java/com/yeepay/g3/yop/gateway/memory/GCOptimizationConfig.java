/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.memory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.logging.Logger;

/**
 * title: GC优化配置管理器<br/>
 * description: 提供G1GC优化配置和JVM参数建议，优化低延迟场景<br/>
 * Copyright: Copyright (c) 2025<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025/1/6
 */
public class GCOptimizationConfig {

    private static final Logger log = Logger.getLogger(GCOptimizationConfig.class.getName());
    private static final GCOptimizationConfig INSTANCE = new GCOptimizationConfig();

    // 性能目标
    private static final long TARGET_GC_PAUSE_MS = 20; // 目标GC暂停时间 < 20ms
    private static final double TARGET_GC_FREQUENCY = 1.0; // 目标Young GC频率 < 1次/秒
    private static final double HEAP_USAGE_WARNING_THRESHOLD = 0.8; // 堆使用率告警阈值 80%

    // 基础监控指标
    private final GCMetrics metrics = new GCMetrics();

    private GCOptimizationConfig() {
        log.info("GCOptimizationConfig initialized");
    }

    public static GCOptimizationConfig getInstance() {
        return INSTANCE;
    }

    /**
     * 获取G1GC优化的JVM参数
     */
    public List<String> getG1GCOptimizedArgs() {
        List<String> args = new ArrayList<>();
        
        // 基础G1GC配置
        args.add("-XX:+UseG1GC");
        args.add("-XX:MaxGCPauseMillis=" + TARGET_GC_PAUSE_MS);
        args.add("-XX:G1HeapRegionSize=16m");
        
        // 堆大小配置 - 根据可用内存动态计算
        long availableMemory = Runtime.getRuntime().maxMemory();
        long recommendedHeap = Math.max(2048L * 1024 * 1024, availableMemory);
        long heapSizeMB = recommendedHeap / 1024 / 1024;
        
        args.add("-Xms" + heapSizeMB + "m");
        args.add("-Xmx" + heapSizeMB + "m");
        
        // 直接内存配置（堆外内存）
        long directMemoryMB = heapSizeMB / 2;
        args.add("-XX:MaxDirectMemorySize=" + directMemoryMB + "m");
        
        // G1GC调优参数
        args.add("-XX:G1NewSizePercent=20");
        args.add("-XX:G1MaxNewSizePercent=30");
        args.add("-XX:G1MixedGCLiveThresholdPercent=85");
        args.add("-XX:G1MixedGCCountTarget=8");
        args.add("-XX:G1OldCSetRegionThresholdPercent=10");
        
        // 并发线程配置
        int processors = Runtime.getRuntime().availableProcessors();
        args.add("-XX:ParallelGCThreads=" + Math.max(1, processors));
        args.add("-XX:ConcGCThreads=" + Math.max(1, processors / 4));
        
        // 其他优化参数
        args.add("-XX:+UseCompressedOops");
        args.add("-XX:+UseCompressedClassPointers");
        args.add("-XX:+DisableExplicitGC"); // 禁用System.gc()调用
        
        return args;
    }

    /**
     * 获取ZGC优化的JVM参数（Java 11+）
     */
    public List<String> getZGCOptimizedArgs() {
        List<String> args = new ArrayList<>();
        
        // ZGC基础配置
        args.add("-XX:+UnlockExperimentalVMOptions");
        args.add("-XX:+UseZGC");
        
        // 堆大小配置
        long availableMemory = Runtime.getRuntime().maxMemory();
        long recommendedHeap = Math.max(4096L * 1024 * 1024, availableMemory);
        long heapSizeMB = recommendedHeap / 1024 / 1024;
        
        args.add("-Xms" + heapSizeMB + "m");
        args.add("-Xmx" + heapSizeMB + "m");
        
        // ZGC特定优化
        args.add("-XX:+UseLargePages");
        args.add("-XX:+UncommitUnusedMemory");
        
        // 其他优化参数
        args.add("-XX:+UseCompressedClassPointers");
        args.add("-XX:+DisableExplicitGC");
        
        return args;
    }

    /**
     * 获取Parallel GC优化的JVM参数
     */
    public List<String> getParallelGCOptimizedArgs() {
        List<String> args = new ArrayList<>();
        
        // Parallel GC配置
        args.add("-XX:+UseParallelGC");
        args.add("-XX:+UseParallelOldGC");
        
        // 堆大小配置
        long availableMemory = Runtime.getRuntime().maxMemory();
        long recommendedHeap = Math.max(2048L * 1024 * 1024, availableMemory);
        long heapSizeMB = recommendedHeap / 1024 / 1024;
        
        args.add("-Xms" + heapSizeMB + "m");
        args.add("-Xmx" + heapSizeMB + "m");
        
        // 新生代配置
        args.add("-XX:NewRatio=3");
        args.add("-XX:SurvivorRatio=8");
        
        // 并发线程配置
        int processors = Runtime.getRuntime().availableProcessors();
        args.add("-XX:ParallelGCThreads=" + Math.max(1, processors));
        
        // 其他优化参数
        args.add("-XX:+UseCompressedOops");
        args.add("-XX:+UseCompressedClassPointers");
        args.add("-XX:+DisableExplicitGC");
        
        return args;
    }

    /**
     * 获取GC日志配置参数
     */
    public List<String> getGCLoggingArgs() {
        List<String> args = new ArrayList<>();
        
        // GC日志配置
        args.add("-XX:+PrintGC");
        args.add("-XX:+PrintGCDetails");
        args.add("-XX:+PrintGCTimeStamps");
        args.add("-XX:+PrintGCDateStamps");
        args.add("-XX:+PrintGCApplicationStoppedTime");
        args.add("-Xloggc:logs/gc-%t.log");
        args.add("-XX:+UseGCLogFileRotation");
        args.add("-XX:NumberOfGCLogFiles=5");
        args.add("-XX:GCLogFileSize=100M");
        
        return args;
    }

    /**
     * 获取JVM诊断参数
     */
    public List<String> getJVMDiagnosticArgs() {
        List<String> args = new ArrayList<>();
        
        // OOM时自动dump堆
        args.add("-XX:+HeapDumpOnOutOfMemoryError");
        args.add("-XX:HeapDumpPath=logs/heapdump-%t.hprof");
        
        // JFR配置（Java Flight Recorder）
        args.add("-XX:+FlightRecorder");
        args.add("-XX:StartFlightRecording=duration=60s,filename=logs/startup.jfr");
        
        // 其他诊断参数
        args.add("-XX:+PrintStringDeduplicationStatistics");
        args.add("-XX:+LogVMOutput");
        args.add("-XX:LogFile=logs/jvm-%t.log");
        
        return args;
    }

    /**
     * 获取完整的推荐JVM参数（G1GC + 诊断）
     */
    public List<String> getRecommendedJVMArgs() {
        List<String> args = new ArrayList<>();
        
        // 添加G1GC优化参数
        args.addAll(getG1GCOptimizedArgs());
        
        // 添加GC日志参数
        args.addAll(getGCLoggingArgs());
        
        // 添加诊断参数
        args.addAll(getJVMDiagnosticArgs());
        
        return args;
    }

    /**
     * 生成JVM启动脚本
     */
    public String generateStartupScript(String mainClass, String classpath) {
        StringBuilder script = new StringBuilder();
        
        script.append("#!/bin/bash\n\n");
        script.append("# YOP Gateway Optimized Startup Script\n");
        script.append("# Generated by GCOptimizationConfig\n\n");
        
        script.append("# Create logs directory\n");
        script.append("mkdir -p logs\n\n");
        
        script.append("# JVM Arguments\n");
        script.append("JVM_ARGS=\"");
        
        List<String> args = getRecommendedJVMArgs();
        for (int i = 0; i < args.size(); i++) {
            script.append(args.get(i));
            if (i < args.size() - 1) {
                script.append(" \\\n    ");
            }
        }
        
        script.append("\"\n\n");
        
        script.append("# Start application\n");
        script.append("java $JVM_ARGS -cp ").append(classpath)
              .append(" ").append(mainClass).append(" $@\n");
        
        return script.toString();
    }

    /**
     * 获取内存配置建议
     */
    public MemoryRecommendation getMemoryRecommendation() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long maxMemory = runtime.maxMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        MemoryRecommendation recommendation = new MemoryRecommendation();
        recommendation.setCurrentHeapUsed(usedMemory);
        recommendation.setCurrentHeapMax(maxMemory);
        recommendation.setHeapUsageRatio((double) usedMemory / maxMemory);
        
        // 计算推荐的堆大小
        long recommendedHeap = Math.max(2048L * 1024 * 1024, maxMemory * 2);
        recommendation.setRecommendedHeapSize(recommendedHeap);
        recommendation.setRecommendedDirectMemorySize(recommendedHeap / 2);
        
        // 性能评估
        if (recommendation.getHeapUsageRatio() > HEAP_USAGE_WARNING_THRESHOLD) {
            recommendation.addWarning("堆内存使用率过高: " + 
                String.format("%.2f%%", recommendation.getHeapUsageRatio() * 100));
        }
        
        if (maxMemory < 2048L * 1024 * 1024) {
            recommendation.addWarning("堆内存大小过小，建议至少2GB");
        }
        
        return recommendation;
    }

    /**
     * 获取GC调优建议
     */
    public List<String> getGCTuningRecommendations() {
        List<String> recommendations = new ArrayList<>();
        
        recommendations.add("1. 使用G1GC实现低延迟目标:");
        recommendations.add("   G1GC在高吞吐量和低延迟之间提供良好平衡");
        
        recommendations.add("2. 设置合理的暂停时间目标:");
        recommendations.add("   -XX:MaxGCPauseMillis=20 (目标20ms以下)");
        
        recommendations.add("3. 优化堆外内存使用:");
        recommendations.add("   增加直接内存配置，减少堆内存压力");
        
        recommendations.add("4. 启用对象池化:");
        recommendations.add("   减少临时对象创建，降低GC频率");
        
        recommendations.add("5. 监控GC性能:");
        recommendations.add("   启用详细的GC日志，使用GC分析工具");
        
        return recommendations;
    }

    /**
     * 获取监控指标
     */
    public GCMetrics getMetrics() {
        return metrics;
    }

    /**
     * GC监控指标
     */
    public static class GCMetrics {
        private final LongAdder gcCount = new LongAdder();
        private final LongAdder gcTime = new LongAdder();
        private final AtomicLong lastGCTime = new AtomicLong(System.currentTimeMillis());
        private final AtomicLong startTime = new AtomicLong(System.currentTimeMillis());

        public void recordGC(long durationMs) {
            gcCount.increment();
            gcTime.add(durationMs);
            lastGCTime.set(System.currentTimeMillis());
        }

        public long getGCCount() {
            return gcCount.sum();
        }

        public long getTotalGCTime() {
            return gcTime.sum();
        }

        public double getAverageGCTime() {
            long count = gcCount.sum();
            return count > 0 ? (double) gcTime.sum() / count : 0.0;
        }

        public double getGCFrequency() {
            long elapsed = System.currentTimeMillis() - startTime.get();
            return elapsed > 0 ? (double) gcCount.sum() / (elapsed / 1000.0) : 0.0;
        }

        public long getTimeSinceLastGC() {
            return System.currentTimeMillis() - lastGCTime.get();
        }
    }

    /**
     * 内存配置建议
     */
    public static class MemoryRecommendation {
        private long currentHeapUsed;
        private long currentHeapMax;
        private double heapUsageRatio;
        private long recommendedHeapSize;
        private long recommendedDirectMemorySize;
        private final List<String> warnings = new ArrayList<>();

        // Getters and Setters
        public long getCurrentHeapUsed() { return currentHeapUsed; }
        public void setCurrentHeapUsed(long currentHeapUsed) { this.currentHeapUsed = currentHeapUsed; }

        public long getCurrentHeapMax() { return currentHeapMax; }
        public void setCurrentHeapMax(long currentHeapMax) { this.currentHeapMax = currentHeapMax; }

        public double getHeapUsageRatio() { return heapUsageRatio; }
        public void setHeapUsageRatio(double heapUsageRatio) { this.heapUsageRatio = heapUsageRatio; }

        public long getRecommendedHeapSize() { return recommendedHeapSize; }
        public void setRecommendedHeapSize(long recommendedHeapSize) { this.recommendedHeapSize = recommendedHeapSize; }

        public long getRecommendedDirectMemorySize() { return recommendedDirectMemorySize; }
        public void setRecommendedDirectMemorySize(long recommendedDirectMemorySize) { this.recommendedDirectMemorySize = recommendedDirectMemorySize; }

        public List<String> getWarnings() { return warnings; }
        public void addWarning(String warning) { warnings.add(warning); }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("Memory Recommendation:\n");
            sb.append("  Current Heap: ").append(currentHeapUsed / 1024 / 1024)
              .append("MB / ").append(currentHeapMax / 1024 / 1024)
              .append("MB (").append(String.format("%.2f%%", heapUsageRatio * 100)).append(")\n");
            sb.append("  Recommended Heap: ").append(recommendedHeapSize / 1024 / 1024).append("MB\n");
            sb.append("  Recommended Direct Memory: ").append(recommendedDirectMemorySize / 1024 / 1024).append("MB\n");
            
            if (!warnings.isEmpty()) {
                sb.append("  Warnings:\n");
                for (String warning : warnings) {
                    sb.append("    - ").append(warning).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
}